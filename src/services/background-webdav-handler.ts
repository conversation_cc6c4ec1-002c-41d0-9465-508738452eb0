/**
 * Background Script WebDAV处理器
 * 在background环境中处理WebDAV请求，避免浏览器限制
 */

import type { 
  WebDAVConfig, 
  WebDAVResponse, 
  WebDAVFileInfo,
  WebDAVOperation 
} from '@/types/webdav'

import { 
  WEBDAV_STATUS_CODES,
  WEBDAV_ERROR_CODES,
  WEBDAV_HEADERS,
  WEBDAV_CONTENT_TYPES
} from '@/constants/webdav'

/**
 * Background WebDAV处理器
 */
export class BackgroundWebDAVHandler {
  /**
   * 处理WebDAV请求
   */
  static async handleMessage(message: any, sender: chrome.runtime.MessageSender): Promise<any> {
    if (!message.action || !message.action.startsWith('WEBDAV_')) {
      return null
    }

    try {
      switch (message.action) {
        case 'WEBDAV_TEST_CONNECTION':
          return await this.testConnection(message.data.config)
        
        case 'WEBDAV_CREATE_DIRECTORY':
          return await this.createDirectory(message.data.config, message.data.path)
        
        case 'WEBDAV_EXISTS':
          return await this.exists(message.data.config, message.data.path)
        
        case 'WEBDAV_GET_FILE_INFO':
          return await this.getFileInfo(message.data.config, message.data.path)
        
        case 'WEBDAV_UPLOAD_FILE':
          return await this.uploadFile(message.data.config, message.data.path, message.data.content)
        
        case 'WEBDAV_DOWNLOAD_FILE':
          return await this.downloadFile(message.data.config, message.data.path)
        
        default:
          return {
            success: false,
            error: `Unknown WebDAV action: ${message.action}`
          }
      }
    } catch (error) {
      console.error('[BackgroundWebDAV] Error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 测试连接 - 改进版本，处理目录不存在的情况
   */
  private static async testConnection(config: WebDAVConfig): Promise<WebDAVResponse<boolean>> {
    try {
      // 首先测试根路径连接
      const rootUrl = config.serverUrl.replace(/\/$/, '')
      const rootResponse = await this.request('PROPFIND', rootUrl, config, {
        [WEBDAV_HEADERS.DEPTH]: '0'
      })

      console.log(`[BackgroundWebDAV] Root test: ${rootResponse.status}`)

      // 检查根路径是否可访问
      if (rootResponse.status === WEBDAV_STATUS_CODES.UNAUTHORIZED) {
        return {
          success: false,
          error: '认证失败，请检查用户名和密码',
          statusCode: rootResponse.status
        }
      }

      if (rootResponse.status === WEBDAV_STATUS_CODES.FORBIDDEN) {
        return {
          success: false,
          error: '权限不足，请检查账户权限',
          statusCode: rootResponse.status
        }
      }

      if (rootResponse.status >= 500) {
        return {
          success: false,
          error: '服务器错误，请稍后重试',
          statusCode: rootResponse.status
        }
      }

      // 根路径可访问，现在检查备份目录
      const backupUrl = this.buildUrl(config, '')
      const backupResponse = await this.request('PROPFIND', backupUrl, config, {
        [WEBDAV_HEADERS.DEPTH]: '0'
      })

      console.log(`[BackgroundWebDAV] Backup dir test: ${backupResponse.status}`)

      if (backupResponse.status === WEBDAV_STATUS_CODES.MULTI_STATUS) {
        // 备份目录已存在
        return {
          success: true,
          data: true,
          statusCode: backupResponse.status
        }
      } else if (backupResponse.status === WEBDAV_STATUS_CODES.NOT_FOUND) {
        // 备份目录不存在，尝试创建
        console.log(`[BackgroundWebDAV] Creating backup directory: ${config.backupPath}`)
        
        const createResponse = await this.request('MKCOL', backupUrl, config)
        console.log(`[BackgroundWebDAV] Create result: ${createResponse.status}`)

        if (createResponse.status === WEBDAV_STATUS_CODES.CREATED || 
            createResponse.status === WEBDAV_STATUS_CODES.OK) {
          return {
            success: true,
            data: true,
            statusCode: createResponse.status
          }
        } else {
          return {
            success: false,
            error: `无法创建备份目录，状态码：${createResponse.status}`,
            statusCode: createResponse.status
          }
        }
      } else {
        return {
          success: false,
          error: `备份目录访问失败，状态码：${backupResponse.status}`,
          statusCode: backupResponse.status
        }
      }
    } catch (error) {
      return this.handleError(error, 'testConnection')
    }
  }

  /**
   * 创建目录
   */
  private static async createDirectory(config: WebDAVConfig, path: string): Promise<WebDAVResponse<boolean>> {
    try {
      const url = this.buildUrl(config, path)
      const response = await this.request('MKCOL', url, config)
      
      const success = response.status === WEBDAV_STATUS_CODES.CREATED || 
                     response.status === WEBDAV_STATUS_CODES.OK

      return {
        success,
        data: success,
        statusCode: response.status
      }
    } catch (error) {
      return this.handleError(error, 'createDirectory')
    }
  }

  /**
   * 检查文件是否存在
   */
  private static async exists(config: WebDAVConfig, path: string): Promise<WebDAVResponse<boolean>> {
    try {
      const url = this.buildUrl(config, path)
      const response = await this.request('PROPFIND', url, config, {
        [WEBDAV_HEADERS.DEPTH]: '0'
      })

      const exists = response.status === WEBDAV_STATUS_CODES.MULTI_STATUS
      
      return {
        success: true,
        data: exists,
        statusCode: response.status
      }
    } catch (error) {
      // 404表示不存在，这是正常情况
      if (error instanceof Error && error.message.includes('404')) {
        return {
          success: true,
          data: false,
          statusCode: 404
        }
      }
      return this.handleError(error, 'exists')
    }
  }

  /**
   * 获取文件信息
   */
  private static async getFileInfo(config: WebDAVConfig, path: string): Promise<WebDAVResponse<WebDAVFileInfo>> {
    try {
      const url = this.buildUrl(config, path)
      const response = await this.request('PROPFIND', url, config, {
        [WEBDAV_HEADERS.DEPTH]: '0'
      })

      if (response.status !== WEBDAV_STATUS_CODES.MULTI_STATUS) {
        throw new Error(`Failed to get file info: ${response.status}`)
      }

      const text = await response.text()
      const fileInfo = this.parseFileInfo(text, path)

      return {
        success: true,
        data: fileInfo,
        statusCode: response.status
      }
    } catch (error) {
      return this.handleError(error, 'getFileInfo')
    }
  }

  /**
   * 上传文件
   */
  private static async uploadFile(config: WebDAVConfig, path: string, content: string): Promise<WebDAVResponse<boolean>> {
    try {
      const url = this.buildUrl(config, path)
      const response = await this.request('PUT', url, config, {
        [WEBDAV_HEADERS.CONTENT_TYPE]: WEBDAV_CONTENT_TYPES.JSON
      }, content)

      const success = response.status === WEBDAV_STATUS_CODES.CREATED || 
                     response.status === WEBDAV_STATUS_CODES.NO_CONTENT ||
                     response.status === WEBDAV_STATUS_CODES.OK

      return {
        success,
        data: success,
        statusCode: response.status
      }
    } catch (error) {
      return this.handleError(error, 'uploadFile')
    }
  }

  /**
   * 下载文件
   */
  private static async downloadFile(config: WebDAVConfig, path: string): Promise<WebDAVResponse<string>> {
    try {
      const url = this.buildUrl(config, path)
      const response = await this.request('GET', url, config)

      if (response.status !== WEBDAV_STATUS_CODES.OK) {
        throw new Error(`Failed to download file: ${response.status}`)
      }

      const content = await response.text()

      return {
        success: true,
        data: content,
        statusCode: response.status
      }
    } catch (error) {
      return this.handleError(error, 'downloadFile')
    }
  }

  /**
   * 发送HTTP请求（在background环境中）
   */
  private static async request(
    method: WebDAVOperation,
    url: string,
    config: WebDAVConfig,
    headers: Record<string, string> = {},
    body?: string
  ): Promise<Response> {
    const auth = 'Basic ' + btoa(`${config.username}:${config.password}`)

    const requestInit: RequestInit = {
      method,
      headers: {
        [WEBDAV_HEADERS.AUTHORIZATION]: auth,
        ...headers
      }
    }

    if (body) {
      requestInit.body = body
    }

    console.log(`[BackgroundWebDAV] ${method} ${url}`)

    const response = await fetch(url, requestInit)
    
    console.log(`[BackgroundWebDAV] Response: ${response.status}`)
    
    return response
  }

  /**
   * 构建URL
   */
  private static buildUrl(config: WebDAVConfig, path: string): string {
    const baseUrl = config.serverUrl.replace(/\/$/, '')
    const backupPath = config.backupPath.replace(/^\//, '').replace(/\/$/, '')
    const filePath = path.replace(/^\//, '')
    
    return `${baseUrl}/${backupPath}${filePath ? '/' + filePath : ''}`
  }

  /**
   * 解析文件信息
   */
  private static parseFileInfo(xmlText: string, path: string): WebDAVFileInfo {
    const name = path.split('/').pop() || ''
    const isDirectory = xmlText.includes('<d:collection/>') || xmlText.includes('httpd/unix-directory')
    
    const sizeMatch = xmlText.match(/<d:getcontentlength>(\d+)<\/d:getcontentlength>/)
    const size = sizeMatch ? parseInt(sizeMatch[1], 10) : 0
    
    const dateMatch = xmlText.match(/<d:getlastmodified>([^<]+)<\/d:getlastmodified>/)
    const lastModified = dateMatch ? new Date(dateMatch[1]) : new Date()

    return {
      path,
      name,
      isDirectory,
      size,
      lastModified,
      contentType: isDirectory ? WEBDAV_CONTENT_TYPES.DIRECTORY : WEBDAV_CONTENT_TYPES.JSON
    }
  }

  /**
   * 错误处理 - 改进版本，提供更友好的错误信息
   */
  private static handleError(error: unknown, operation: string): WebDAVResponse<any> {
    let errorCode = WEBDAV_ERROR_CODES.NETWORK_ERROR
    let message = 'Unknown error'

    if (error instanceof Error) {
      message = error.message
      
      if (message.includes('401') || message.includes('Unauthorized')) {
        errorCode = WEBDAV_ERROR_CODES.AUTH_FAILED
        message = '认证失败，请检查用户名和密码是否正确'
      } else if (message.includes('404') || message.includes('Not Found')) {
        errorCode = WEBDAV_ERROR_CODES.FILE_NOT_FOUND
        message = operation === 'testConnection' ? 
          '服务器连接正常，但备份路径不存在（将自动创建）' : 
          '请求的文件或目录不存在'
      } else if (message.includes('403') || message.includes('Forbidden')) {
        errorCode = WEBDAV_ERROR_CODES.PERMISSION_DENIED
        message = '权限不足，请检查账户是否有读写权限'
      } else if (message.includes('timeout')) {
        errorCode = WEBDAV_ERROR_CODES.CONNECTION_TIMEOUT
        message = '连接超时，请检查网络连接或服务器状态'
      } else if (message.includes('500') || message.includes('502') || message.includes('503')) {
        errorCode = WEBDAV_ERROR_CODES.SERVER_ERROR
        message = '服务器内部错误，请稍后重试'
      } else if (message.includes('Failed to fetch') || message.includes('NetworkError')) {
        errorCode = WEBDAV_ERROR_CODES.NETWORK_ERROR
        message = '网络连接失败，请检查服务器地址和网络连接'
      }
    }

    console.error(`[BackgroundWebDAV] Error in ${operation}:`, { errorCode, message, originalError: error })

    return {
      success: false,
      error: message
    }
  }
}
