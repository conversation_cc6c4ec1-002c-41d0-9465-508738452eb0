/**
 * 冲突解决服务
 */

import type { 
  ConflictType, 
  SyncDataWrapper, 
  BackupHistory 
} from '@/types/sync'
import type { BackupData, Bookmark, BookmarkCategory } from '@/types'

import { 
  STORAGE_KEYS,
  DEBUG_OPTIONS 
} from '@/constants/webdav'

/**
 * 数据差异信息
 */
export interface DataDifference {
  /** 差异类型 */
  type: 'added' | 'removed' | 'modified'
  /** 数据类型 */
  dataType: 'bookmark' | 'category' | 'setting'
  /** 项目ID */
  id: string
  /** 项目名称 */
  name: string
  /** 本地值 */
  localValue?: any
  /** 远程值 */
  remoteValue?: any
}

/**
 * 冲突分析结果
 */
export interface ConflictAnalysis {
  /** 冲突类型 */
  conflictType: ConflictType
  /** 是否有实质性差异 */
  hasSignificantDifferences: boolean
  /** 差异列表 */
  differences: DataDifference[]
  /** 本地数据时间戳 */
  localTimestamp: number
  /** 远程数据时间戳 */
  remoteTimestamp: number
  /** 建议的解决方案 */
  recommendedAction: 'keep_local' | 'keep_remote' | 'merge' | 'manual'
}

/**
 * 合并选项
 */
export interface MergeOptions {
  /** 书签冲突时优先选择 */
  bookmarkPriority: 'local' | 'remote' | 'newer'
  /** 分类冲突时优先选择 */
  categoryPriority: 'local' | 'remote' | 'newer'
  /** 设置冲突时优先选择 */
  settingsPriority: 'local' | 'remote' | 'newer'
  /** 是否保留被删除的项目 */
  preserveDeleted: boolean
}

/**
 * 冲突解决器
 */
export class ConflictResolver {
  private static instance: ConflictResolver

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): ConflictResolver {
    if (!ConflictResolver.instance) {
      ConflictResolver.instance = new ConflictResolver()
    }
    return ConflictResolver.instance
  }

  /**
   * 分析数据冲突
   */
  analyzeConflict(
    localData: SyncDataWrapper<BackupData> | null,
    remoteData: SyncDataWrapper<BackupData> | null
  ): ConflictAnalysis {
    // 基础冲突类型检测
    const conflictType = this.detectBasicConflictType(localData, remoteData)
    
    if (!localData || !remoteData) {
      return {
        conflictType,
        hasSignificantDifferences: true,
        differences: [],
        localTimestamp: localData?.metadata.timestamp || 0,
        remoteTimestamp: remoteData?.metadata.timestamp || 0,
        recommendedAction: this.getRecommendedAction(conflictType)
      }
    }

    // 详细差异分析
    const differences = this.analyzeDataDifferences(
      localData.content, 
      remoteData.content
    )

    const hasSignificantDifferences = differences.length > 0
    const recommendedAction = this.determineRecommendedAction(
      conflictType, 
      differences,
      localData.metadata.timestamp,
      remoteData.metadata.timestamp
    )

    return {
      conflictType,
      hasSignificantDifferences,
      differences,
      localTimestamp: localData.metadata.timestamp,
      remoteTimestamp: remoteData.metadata.timestamp,
      recommendedAction
    }
  }

  /**
   * 智能合并数据
   */
  mergeData(
    localData: SyncDataWrapper<BackupData>,
    remoteData: SyncDataWrapper<BackupData>,
    options: MergeOptions
  ): BackupData {
    this.logDebug('Starting data merge', { options })

    const mergedData: BackupData = {
      bookmarks: this.mergeBookmarks(
        localData.content.bookmarks,
        remoteData.content.bookmarks,
        options
      ),
      categories: this.mergeCategories(
        localData.content.categories,
        remoteData.content.categories,
        options
      ),
      settings: this.mergeSettings(
        localData.content.settings,
        remoteData.content.settings,
        options
      ),
      networkMode: this.mergeNetworkMode(
        localData.content.networkMode,
        remoteData.content.networkMode,
        localData.metadata.timestamp,
        remoteData.metadata.timestamp
      ),
      timestamp: Math.max(localData.metadata.timestamp, remoteData.metadata.timestamp),
      version: this.mergeVersion(localData.content.version, remoteData.content.version)
    }

    this.logDebug('Data merge completed', mergedData)
    return mergedData
  }

  /**
   * 保存冲突解决历史
   */
  async saveConflictHistory(
    analysis: ConflictAnalysis,
    resolution: 'local' | 'remote' | 'merged',
    mergedData?: BackupData
  ): Promise<void> {
    try {
      const historyEntry: BackupHistory = {
        id: `conflict_${Date.now()}`,
        timestamp: Date.now(),
        size: mergedData ? JSON.stringify(mergedData).length : 0,
        description: `Conflict resolved: ${resolution} (${analysis.differences.length} differences)`,
        type: 'manual',
        isCurrent: true
      }

      // 获取现有历史
      const result = await chrome.storage.local.get(STORAGE_KEYS.BACKUP_HISTORY)
      const history: BackupHistory[] = result[STORAGE_KEYS.BACKUP_HISTORY] || []

      // 标记其他条目为非当前
      history.forEach(entry => { entry.isCurrent = false })

      // 添加新条目
      history.unshift(historyEntry)

      // 保留最近10条记录
      const trimmedHistory = history.slice(0, 10)

      await chrome.storage.local.set({
        [STORAGE_KEYS.BACKUP_HISTORY]: trimmedHistory
      })

      this.logDebug('Conflict history saved', historyEntry)

    } catch (error) {
      this.logDebug('Failed to save conflict history:', error)
    }
  }

  /**
   * 检测基础冲突类型
   */
  private detectBasicConflictType(
    localData: SyncDataWrapper<BackupData> | null,
    remoteData: SyncDataWrapper<BackupData> | null
  ): ConflictType {
    if (!remoteData) {
      return localData ? 'UPLOAD_LOCAL' : 'NO_ACTION'
    }

    if (!localData) {
      return 'DOWNLOAD_REMOTE'
    }

    if (localData.metadata.hash === remoteData.metadata.hash) {
      return 'NO_CONFLICT'
    }

    if (localData.metadata.timestamp > remoteData.metadata.timestamp) {
      return 'LOCAL_NEWER'
    }

    if (remoteData.metadata.timestamp > localData.metadata.timestamp) {
      return 'REMOTE_NEWER'
    }

    return 'NO_ACTION'
  }

  /**
   * 分析数据差异
   */
  private analyzeDataDifferences(
    localData: BackupData,
    remoteData: BackupData
  ): DataDifference[] {
    const differences: DataDifference[] = []

    // 分析书签差异
    differences.push(...this.analyzeBookmarkDifferences(
      localData.bookmarks,
      remoteData.bookmarks
    ))

    // 分析分类差异
    differences.push(...this.analyzeCategoryDifferences(
      localData.categories,
      remoteData.categories
    ))

    // 分析设置差异
    differences.push(...this.analyzeSettingsDifferences(
      localData.settings,
      remoteData.settings
    ))

    return differences
  }

  /**
   * 分析书签差异
   */
  private analyzeBookmarkDifferences(
    localBookmarks: Bookmark[],
    remoteBookmarks: Bookmark[]
  ): DataDifference[] {
    const differences: DataDifference[] = []
    const localMap = new Map(localBookmarks.map(b => [b.id, b]))
    const remoteMap = new Map(remoteBookmarks.map(b => [b.id, b]))

    // 检查新增的书签
    for (const [id, bookmark] of remoteMap) {
      if (!localMap.has(id)) {
        differences.push({
          type: 'added',
          dataType: 'bookmark',
          id,
          name: bookmark.title,
          remoteValue: bookmark
        })
      }
    }

    // 检查删除的书签
    for (const [id, bookmark] of localMap) {
      if (!remoteMap.has(id)) {
        differences.push({
          type: 'removed',
          dataType: 'bookmark',
          id,
          name: bookmark.title,
          localValue: bookmark
        })
      }
    }

    // 检查修改的书签
    for (const [id, localBookmark] of localMap) {
      const remoteBookmark = remoteMap.get(id)
      if (remoteBookmark && !this.areBookmarksEqual(localBookmark, remoteBookmark)) {
        differences.push({
          type: 'modified',
          dataType: 'bookmark',
          id,
          name: localBookmark.title,
          localValue: localBookmark,
          remoteValue: remoteBookmark
        })
      }
    }

    return differences
  }

  /**
   * 分析分类差异
   */
  private analyzeCategoryDifferences(
    localCategories: BookmarkCategory[],
    remoteCategories: BookmarkCategory[]
  ): DataDifference[] {
    const differences: DataDifference[] = []
    const localMap = new Map(localCategories.map(c => [c.id, c]))
    const remoteMap = new Map(remoteCategories.map(c => [c.id, c]))

    // 检查新增的分类
    for (const [id, category] of remoteMap) {
      if (!localMap.has(id)) {
        differences.push({
          type: 'added',
          dataType: 'category',
          id,
          name: category.name,
          remoteValue: category
        })
      }
    }

    // 检查删除的分类
    for (const [id, category] of localMap) {
      if (!remoteMap.has(id)) {
        differences.push({
          type: 'removed',
          dataType: 'category',
          id,
          name: category.name,
          localValue: category
        })
      }
    }

    // 检查修改的分类
    for (const [id, localCategory] of localMap) {
      const remoteCategory = remoteMap.get(id)
      if (remoteCategory && !this.areCategoriesEqual(localCategory, remoteCategory)) {
        differences.push({
          type: 'modified',
          dataType: 'category',
          id,
          name: localCategory.name,
          localValue: localCategory,
          remoteValue: remoteCategory
        })
      }
    }

    return differences
  }

  /**
   * 分析设置差异
   */
  private analyzeSettingsDifferences(
    localSettings: any,
    remoteSettings: any
  ): DataDifference[] {
    const differences: DataDifference[] = []
    const allKeys = new Set([...Object.keys(localSettings), ...Object.keys(remoteSettings)])

    for (const key of allKeys) {
      const localValue = localSettings[key]
      const remoteValue = remoteSettings[key]

      if (JSON.stringify(localValue) !== JSON.stringify(remoteValue)) {
        differences.push({
          type: localValue === undefined ? 'added' : 
                remoteValue === undefined ? 'removed' : 'modified',
          dataType: 'setting',
          id: key,
          name: key,
          localValue,
          remoteValue
        })
      }
    }

    return differences
  }

  /**
   * 合并书签
   */
  private mergeBookmarks(
    localBookmarks: Bookmark[],
    remoteBookmarks: Bookmark[],
    options: MergeOptions
  ): Bookmark[] {
    const merged = new Map<string, Bookmark>()
    
    // 先添加所有本地书签
    localBookmarks.forEach(bookmark => {
      merged.set(bookmark.id, bookmark)
    })

    // 处理远程书签
    remoteBookmarks.forEach(remoteBookmark => {
      const localBookmark = merged.get(remoteBookmark.id)
      
      if (!localBookmark) {
        // 新书签，直接添加
        merged.set(remoteBookmark.id, remoteBookmark)
      } else {
        // 冲突书签，根据策略选择
        const selectedBookmark = this.selectBookmarkByPriority(
          localBookmark,
          remoteBookmark,
          options.bookmarkPriority
        )
        merged.set(remoteBookmark.id, selectedBookmark)
      }
    })

    return Array.from(merged.values())
  }

  /**
   * 合并分类
   */
  private mergeCategories(
    localCategories: BookmarkCategory[],
    remoteCategories: BookmarkCategory[],
    options: MergeOptions
  ): BookmarkCategory[] {
    const merged = new Map<string, BookmarkCategory>()
    
    localCategories.forEach(category => {
      merged.set(category.id, category)
    })

    remoteCategories.forEach(remoteCategory => {
      const localCategory = merged.get(remoteCategory.id)
      
      if (!localCategory) {
        merged.set(remoteCategory.id, remoteCategory)
      } else {
        const selectedCategory = this.selectCategoryByPriority(
          localCategory,
          remoteCategory,
          options.categoryPriority
        )
        merged.set(remoteCategory.id, selectedCategory)
      }
    })

    return Array.from(merged.values())
  }

  /**
   * 合并设置
   */
  private mergeSettings(
    localSettings: any,
    remoteSettings: any,
    options: MergeOptions
  ): any {
    const merged = { ...localSettings }
    
    Object.keys(remoteSettings).forEach(key => {
      if (!(key in localSettings)) {
        // 新设置，直接添加
        merged[key] = remoteSettings[key]
      } else if (options.settingsPriority === 'remote') {
        // 优先远程
        merged[key] = remoteSettings[key]
      } else if (options.settingsPriority === 'newer') {
        // 这里简化处理，实际可能需要更复杂的逻辑
        merged[key] = remoteSettings[key]
      }
      // local优先则保持原值
    })

    return merged
  }

  /**
   * 合并网络模式
   */
  private mergeNetworkMode(
    localMode: string,
    remoteMode: string,
    localTimestamp: number,
    remoteTimestamp: number
  ): string {
    return remoteTimestamp > localTimestamp ? remoteMode : localMode
  }

  /**
   * 合并版本号
   */
  private mergeVersion(localVersion: string, remoteVersion: string): string {
    // 简单的版本比较，实际可能需要更复杂的语义化版本比较
    return localVersion >= remoteVersion ? localVersion : remoteVersion
  }

  /**
   * 根据优先级选择书签
   */
  private selectBookmarkByPriority(
    local: Bookmark,
    remote: Bookmark,
    priority: 'local' | 'remote' | 'newer'
  ): Bookmark {
    switch (priority) {
      case 'local':
        return local
      case 'remote':
        return remote
      case 'newer':
        return remote.updatedAt > local.updatedAt ? remote : local
      default:
        return local
    }
  }

  /**
   * 根据优先级选择分类
   */
  private selectCategoryByPriority(
    local: BookmarkCategory,
    remote: BookmarkCategory,
    priority: 'local' | 'remote' | 'newer'
  ): BookmarkCategory {
    switch (priority) {
      case 'local':
        return local
      case 'remote':
        return remote
      case 'newer':
        return remote.updatedAt > local.updatedAt ? remote : local
      default:
        return local
    }
  }

  /**
   * 比较书签是否相等
   */
  private areBookmarksEqual(a: Bookmark, b: Bookmark): boolean {
    const keys: (keyof Bookmark)[] = [
      'title', 'url', 'description', 'icon', 'iconType', 
      'iconText', 'iconColor', 'categoryId', 'position'
    ]
    
    return keys.every(key => a[key] === b[key])
  }

  /**
   * 比较分类是否相等
   */
  private areCategoriesEqual(a: BookmarkCategory, b: BookmarkCategory): boolean {
    return a.name === b.name && 
           a.icon === b.icon && 
           a.color === b.color &&
           JSON.stringify(a.bookmarks.sort()) === JSON.stringify(b.bookmarks.sort())
  }

  /**
   * 获取建议的操作
   */
  private getRecommendedAction(conflictType: ConflictType): 'keep_local' | 'keep_remote' | 'merge' | 'manual' {
    switch (conflictType) {
      case 'LOCAL_NEWER':
      case 'UPLOAD_LOCAL':
        return 'keep_local'
      case 'REMOTE_NEWER':
      case 'DOWNLOAD_REMOTE':
        return 'keep_remote'
      case 'NO_CONFLICT':
      case 'NO_ACTION':
        return 'keep_local'
      default:
        return 'manual'
    }
  }

  /**
   * 确定建议的操作
   */
  private determineRecommendedAction(
    conflictType: ConflictType,
    differences: DataDifference[],
    localTimestamp: number,
    remoteTimestamp: number
  ): 'keep_local' | 'keep_remote' | 'merge' | 'manual' {
    // 如果差异很少且都是新增/修改，建议合并
    if (differences.length <= 5 && 
        differences.every(d => d.type !== 'removed')) {
      return 'merge'
    }

    // 根据时间戳决定
    return remoteTimestamp > localTimestamp ? 'keep_remote' : 'keep_local'
  }

  /**
   * 调试日志
   */
  private logDebug(message: string, data?: any): void {
    if (DEBUG_OPTIONS.ENABLE_SYNC_LOGS) {
      console.log(`${DEBUG_OPTIONS.LOG_PREFIX}[ConflictResolver] ${message}`, data)
    }
  }
}

/**
 * 获取冲突解决器实例
 */
export function getConflictResolver(): ConflictResolver {
  return ConflictResolver.getInstance()
}

/**
 * 便捷方法：分析冲突
 */
export function analyzeDataConflict(
  localData: SyncDataWrapper<BackupData> | null,
  remoteData: SyncDataWrapper<BackupData> | null
): ConflictAnalysis {
  return getConflictResolver().analyzeConflict(localData, remoteData)
}

/**
 * 便捷方法：合并数据
 */
export function mergeConflictData(
  localData: SyncDataWrapper<BackupData>,
  remoteData: SyncDataWrapper<BackupData>,
  options: MergeOptions
): BackupData {
  return getConflictResolver().mergeData(localData, remoteData, options)
}
