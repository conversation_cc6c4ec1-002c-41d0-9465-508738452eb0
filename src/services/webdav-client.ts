/**
 * WebDAV客户端服务
 */

import type { 
  WebDAVConfig, 
  WebDAVResponse, 
  WebDAVFileInfo, 
  WebDAVClientOptions,
  WebDAVAuth,
  WebDAVOperation 
} from '@/types/webdav'

import { 
  WEBDAV_DEFAULTS,
  WEBDAV_STATUS_CODES,
  WEBDAV_ERROR_CODES,
  WEBDAV_HEADERS,
  WEBDAV_CONTENT_TYPES,
  DEBUG_OPTIONS
} from '@/constants/webdav'

import { webdavRequest } from '@/utils/webdav-request'

/**
 * WebDAV客户端类
 */
export class WebDAVClient {
  private config: WebDAVConfig
  private options: WebDAVClientOptions

  constructor(config: WebDAVConfig, options: WebDAVClientOptions = {}) {
    this.config = config
    this.options = {
      timeout: WEBDAV_DEFAULTS.TIMEOUT,
      maxRetries: WEBDAV_DEFAULTS.MAX_RETRIES,
      retryDelay: WEBDAV_DEFAULTS.RETRY_DELAY,
      strictSSL: true,
      ...options
    }
  }

  /**
   * 测试WebDAV连接 - 改进版本，避免浏览器认证弹窗
   */
  async testConnection(): Promise<WebDAVResponse<boolean>> {
    try {
      // 首先尝试不带认证的请求，检查服务器是否可达
      const urlCheck = await this.checkServerReachability()
      if (!urlCheck.success) {
        return urlCheck
      }

      // 然后进行带认证的PROPFIND请求
      const response = await this.request('PROPFIND', '', {
        headers: {
          [WEBDAV_HEADERS.DEPTH]: '0'
        }
      })

      const success = response.status === WEBDAV_STATUS_CODES.MULTI_STATUS ||
                     response.status === WEBDAV_STATUS_CODES.OK

      return {
        success,
        data: success,
        statusCode: response.status
      }
    } catch (error) {
      return this.handleError(error, 'testConnection')
    }
  }

  /**
   * 检查服务器可达性（不带认证）
   */
  private async checkServerReachability(): Promise<WebDAVResponse<boolean>> {
    try {
      const baseUrl = this.config.serverUrl.replace(/\/$/, '')
      
      // 使用简单的fetch请求检查服务器是否可达
      const response = await fetch(baseUrl, {
        method: 'HEAD',
        mode: 'no-cors', // 避免CORS问题
        cache: 'no-cache'
      })

      return {
        success: true,
        data: true
      }
    } catch (error) {
      return {
        success: false,
        error: '无法连接到服务器，请检查网络连接和服务器地址'
      }
    }
  }

  /**
   * 创建目录
   */
  async createDirectory(path: string): Promise<WebDAVResponse<boolean>> {
    try {
      const response = await this.request('MKCOL', path)
      
      const success = response.status === WEBDAV_STATUS_CODES.CREATED || 
                     response.status === WEBDAV_STATUS_CODES.OK

      return {
        success,
        data: success,
        statusCode: response.status
      }
    } catch (error) {
      return this.handleError(error, 'createDirectory')
    }
  }

  /**
   * 检查文件或目录是否存在
   */
  async exists(path: string): Promise<WebDAVResponse<boolean>> {
    try {
      const response = await this.request('PROPFIND', path, {
        headers: {
          [WEBDAV_HEADERS.DEPTH]: '0'
        }
      })

      const exists = response.status === WEBDAV_STATUS_CODES.MULTI_STATUS
      
      return {
        success: true,
        data: exists,
        statusCode: response.status
      }
    } catch (error) {
      // 404表示不存在，这是正常情况
      if (error instanceof Error && error.message.includes('404')) {
        return {
          success: true,
          data: false,
          statusCode: 404
        }
      }
      return this.handleError(error, 'exists')
    }
  }

  /**
   * 获取文件信息
   */
  async getFileInfo(path: string): Promise<WebDAVResponse<WebDAVFileInfo>> {
    try {
      const response = await this.request('PROPFIND', path, {
        headers: {
          [WEBDAV_HEADERS.DEPTH]: '0'
        }
      })

      if (response.status !== WEBDAV_STATUS_CODES.MULTI_STATUS) {
        throw new Error(`Failed to get file info: ${response.status}`)
      }

      const text = await response.text()
      const fileInfo = this.parseFileInfo(text, path)

      return {
        success: true,
        data: fileInfo,
        statusCode: response.status
      }
    } catch (error) {
      return this.handleError(error, 'getFileInfo')
    }
  }

  /**
   * 上传文件
   */
  async uploadFile(path: string, content: string): Promise<WebDAVResponse<boolean>> {
    try {
      const response = await this.request('PUT', path, {
        body: content,
        headers: {
          [WEBDAV_HEADERS.CONTENT_TYPE]: WEBDAV_CONTENT_TYPES.JSON
        }
      })

      const success = response.status === WEBDAV_STATUS_CODES.CREATED || 
                     response.status === WEBDAV_STATUS_CODES.NO_CONTENT ||
                     response.status === WEBDAV_STATUS_CODES.OK

      return {
        success,
        data: success,
        statusCode: response.status
      }
    } catch (error) {
      return this.handleError(error, 'uploadFile')
    }
  }

  /**
   * 下载文件
   */
  async downloadFile(path: string): Promise<WebDAVResponse<string>> {
    try {
      const response = await this.request('GET', path)

      if (response.status !== WEBDAV_STATUS_CODES.OK) {
        throw new Error(`Failed to download file: ${response.status}`)
      }

      const content = await response.text()

      return {
        success: true,
        data: content,
        statusCode: response.status
      }
    } catch (error) {
      return this.handleError(error, 'downloadFile')
    }
  }

  /**
   * 执行HTTP请求 - 使用XMLHttpRequest避免浏览器认证弹窗
   */
  private async request(
    method: WebDAVOperation, 
    path: string, 
    options: RequestInit = {}
  ): Promise<Response> {
    const url = this.buildUrl(path)
    const auth = this.buildAuthHeader()

    try {
      this.logDebug(`${method} ${url}`, { headers: options.headers })

      const response = await webdavRequest(method, url, auth, {
        headers: options.headers as Record<string, string>,
        body: options.body as string,
        timeout: this.options.timeout
      })
      
      this.logDebug(`Response: ${response.status}`, response)

      // 将XMLHttpRequest响应包装成类似fetch的Response对象
      return response as any
    } catch (error) {
      if (error instanceof Error && error.message === WEBDAV_ERROR_CODES.CONNECTION_TIMEOUT) {
        throw new Error(WEBDAV_ERROR_CODES.CONNECTION_TIMEOUT)
      }
      
      throw error
    }
  }

  /**
   * 构建完整URL
   */
  private buildUrl(path: string): string {
    const baseUrl = this.config.serverUrl.replace(/\/$/, '')
    const backupPath = this.config.backupPath.replace(/^\//, '').replace(/\/$/, '')
    const filePath = path.replace(/^\//, '')
    
    return `${baseUrl}/${backupPath}${filePath ? '/' + filePath : ''}`
  }

  /**
   * 构建认证头
   */
  private buildAuthHeader(): string {
    const credentials = btoa(`${this.config.username}:${this.config.password}`)
    return `Basic ${credentials}`
  }

  /**
   * 解析文件信息（简化版本）
   */
  private parseFileInfo(xmlText: string, path: string): WebDAVFileInfo {
    // 简化的XML解析，实际生产环境可能需要更完善的XML解析器
    const name = path.split('/').pop() || ''
    const isDirectory = xmlText.includes('<d:collection/>') || xmlText.includes('httpd/unix-directory')
    
    // 尝试从XML中提取信息
    const sizeMatch = xmlText.match(/<d:getcontentlength>(\d+)<\/d:getcontentlength>/)
    const size = sizeMatch ? parseInt(sizeMatch[1], 10) : 0
    
    const dateMatch = xmlText.match(/<d:getlastmodified>([^<]+)<\/d:getlastmodified>/)
    const lastModified = dateMatch ? new Date(dateMatch[1]) : new Date()

    return {
      path,
      name,
      isDirectory,
      size,
      lastModified,
      contentType: isDirectory ? WEBDAV_CONTENT_TYPES.DIRECTORY : WEBDAV_CONTENT_TYPES.JSON
    }
  }

  /**
   * 错误处理
   */
  private handleError(error: unknown, operation: string): WebDAVResponse<any> {
    let errorCode = WEBDAV_ERROR_CODES.NETWORK_ERROR
    let message = 'Unknown error'

    if (error instanceof Error) {
      message = error.message
      
      if (message.includes('401') || message.includes('Unauthorized')) {
        errorCode = WEBDAV_ERROR_CODES.AUTH_FAILED
      } else if (message.includes('404') || message.includes('Not Found')) {
        errorCode = WEBDAV_ERROR_CODES.FILE_NOT_FOUND
      } else if (message.includes('403') || message.includes('Forbidden')) {
        errorCode = WEBDAV_ERROR_CODES.PERMISSION_DENIED
      } else if (message.includes('timeout') || message === WEBDAV_ERROR_CODES.CONNECTION_TIMEOUT) {
        errorCode = WEBDAV_ERROR_CODES.CONNECTION_TIMEOUT
      } else if (message.includes('500') || message.includes('502') || message.includes('503')) {
        errorCode = WEBDAV_ERROR_CODES.SERVER_ERROR
      }
    }

    this.logDebug(`Error in ${operation}:`, { errorCode, message })

    return {
      success: false,
      error: `${errorCode}: ${message}`
    }
  }

  /**
   * 调试日志
   */
  private logDebug(message: string, data?: any): void {
    if (DEBUG_OPTIONS.ENABLE_WEBDAV_LOGS) {
      console.log(`${DEBUG_OPTIONS.LOG_PREFIX} ${message}`, data)
    }
  }
}

/**
 * 创建WebDAV客户端实例
 */
export function createWebDAVClient(
  config: WebDAVConfig, 
  options?: WebDAVClientOptions
): WebDAVClient {
  return new WebDAVClient(config, options)
}
