/**
 * WebDAV配置管理服务
 */

import type { WebDAVConfig } from '@/types/webdav'
import { WEBDAV_DEFAULTS, STORAGE_KEYS, DEBUG_OPTIONS } from '@/constants/webdav'

/**
 * WebDAV配置管理器
 */
export class WebDAVConfigManager {
  private static instance: WebDAVConfigManager
  private config: WebDAVConfig | null = null

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): WebDAVConfigManager {
    if (!WebDAVConfigManager.instance) {
      WebDAVConfigManager.instance = new WebDAVConfigManager()
    }
    return WebDAVConfigManager.instance
  }

  /**
   * 获取WebDAV配置
   */
  async getConfig(): Promise<WebDAVConfig | null> {
    if (this.config) {
      return this.config
    }

    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.WEBDAV_CONFIG)
      const storedConfig = result[STORAGE_KEYS.WEBDAV_CONFIG]

      if (storedConfig) {
        // 解密密码
        const decryptedConfig = {
          ...storedConfig,
          password: this.decryptPassword(storedConfig.password)
        }
        
        this.config = this.validateAndFillDefaults(decryptedConfig)
        this.logDebug('Config loaded from storage', { 
          ...this.config, 
          password: '***' // 不记录密码
        })
      }

      return this.config
    } catch (error) {
      this.logDebug('Failed to load config:', error)
      return null
    }
  }

  /**
   * 保存WebDAV配置
   */
  async saveConfig(config: Partial<WebDAVConfig>): Promise<boolean> {
    try {
      // 验证必要字段
      if (!this.isValidConfig(config)) {
        throw new Error('Invalid WebDAV configuration')
      }

      // 填充默认值
      const fullConfig = this.validateAndFillDefaults(config)
      
      // 加密密码
      const encryptedConfig = {
        ...fullConfig,
        password: this.encryptPassword(fullConfig.password)
      }

      // 保存到存储
      await chrome.storage.local.set({
        [STORAGE_KEYS.WEBDAV_CONFIG]: encryptedConfig
      })

      // 更新内存中的配置
      this.config = fullConfig

      this.logDebug('Config saved successfully', { 
        ...fullConfig, 
        password: '***' 
      })

      return true
    } catch (error) {
      this.logDebug('Failed to save config:', error)
      return false
    }
  }

  /**
   * 删除WebDAV配置
   */
  async deleteConfig(): Promise<boolean> {
    try {
      await chrome.storage.local.remove(STORAGE_KEYS.WEBDAV_CONFIG)
      this.config = null
      
      this.logDebug('Config deleted successfully')
      return true
    } catch (error) {
      this.logDebug('Failed to delete config:', error)
      return false
    }
  }

  /**
   * 检查配置是否存在且有效
   */
  async hasValidConfig(): Promise<boolean> {
    const config = await this.getConfig()
    return config !== null && config.enabled && this.isValidConfig(config)
  }

  /**
   * 更新配置的某个字段
   */
  async updateConfigField<K extends keyof WebDAVConfig>(
    field: K, 
    value: WebDAVConfig[K]
  ): Promise<boolean> {
    const currentConfig = await this.getConfig()
    if (!currentConfig) {
      return false
    }

    const updatedConfig = {
      ...currentConfig,
      [field]: value
    }

    return this.saveConfig(updatedConfig)
  }

  /**
   * 验证配置是否有效
   */
  private isValidConfig(config: Partial<WebDAVConfig>): boolean {
    const requiredFields: (keyof WebDAVConfig)[] = [
      'serverUrl', 
      'username', 
      'password'
    ]

    for (const field of requiredFields) {
      if (!config[field] || String(config[field]).trim() === '') {
        this.logDebug(`Missing required field: ${field}`)
        return false
      }
    }

    // 验证服务器URL格式
    if (config.serverUrl) {
      try {
        new URL(config.serverUrl)
      } catch {
        this.logDebug('Invalid server URL format')
        return false
      }
    }

    return true
  }

  /**
   * 验证并填充默认值
   */
  private validateAndFillDefaults(config: Partial<WebDAVConfig>): WebDAVConfig {
    return {
      serverUrl: config.serverUrl || '',
      username: config.username || '',
      password: config.password || '',
      backupPath: config.backupPath || WEBDAV_DEFAULTS.BACKUP_PATH,
      enabled: config.enabled !== undefined ? config.enabled : true,
      timeout: config.timeout || WEBDAV_DEFAULTS.TIMEOUT
    }
  }

  /**
   * 加密密码（简单的Base64编码，实际生产中可能需要更强的加密）
   */
  private encryptPassword(password: string): string {
    try {
      // 简单的Base64编码 + 固定salt
      const salt = 'dualtab_webdav_salt'
      const combined = salt + password + salt
      return btoa(combined)
    } catch {
      return password // 加密失败时返回原密码
    }
  }

  /**
   * 解密密码
   */
  private decryptPassword(encryptedPassword: string): string {
    try {
      const salt = 'dualtab_webdav_salt'
      const decoded = atob(encryptedPassword)
      
      // 移除前后的salt
      if (decoded.startsWith(salt) && decoded.endsWith(salt)) {
        return decoded.slice(salt.length, -salt.length)
      }
      
      return encryptedPassword // 解密失败时返回原密码
    } catch {
      return encryptedPassword
    }
  }

  /**
   * 清除内存中的配置缓存
   */
  clearCache(): void {
    this.config = null
    this.logDebug('Config cache cleared')
  }

  /**
   * 调试日志
   */
  private logDebug(message: string, data?: any): void {
    if (DEBUG_OPTIONS.ENABLE_WEBDAV_LOGS) {
      console.log(`${DEBUG_OPTIONS.LOG_PREFIX}[Config] ${message}`, data)
    }
  }
}

/**
 * 获取WebDAV配置管理器实例
 */
export function getWebDAVConfigManager(): WebDAVConfigManager {
  return WebDAVConfigManager.getInstance()
}

/**
 * 便捷方法：获取WebDAV配置
 */
export async function getWebDAVConfig(): Promise<WebDAVConfig | null> {
  return getWebDAVConfigManager().getConfig()
}

/**
 * 便捷方法：保存WebDAV配置
 */
export async function saveWebDAVConfig(config: Partial<WebDAVConfig>): Promise<boolean> {
  return getWebDAVConfigManager().saveConfig(config)
}

/**
 * 便捷方法：检查是否有有效配置
 */
export async function hasValidWebDAVConfig(): Promise<boolean> {
  return getWebDAVConfigManager().hasValidConfig()
}

/**
 * 便捷方法：删除WebDAV配置
 */
export async function deleteWebDAVConfig(): Promise<boolean> {
  return getWebDAVConfigManager().deleteConfig()
}
