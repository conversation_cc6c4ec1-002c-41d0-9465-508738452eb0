/**
 * 数据同步服务
 */

import type { 
  SyncResult, 
  ConflictType, 
  DataMetadata, 
  SyncDataWrapper 
} from '@/types/sync'
import type { WebDAVConfig } from '@/types/webdav'
import type { BackupData } from '@/types'

import { ChromeWebDAVClient, createChromeWebDAVClient } from './chrome-webdav-client'
import { getWebDAVConfig } from './webdav-config'
import { 
  WEBDAV_PATHS, 
  WEBDAV_ERROR_CODES, 
  STORAGE_KEYS,
  DEBUG_OPTIONS 
} from '@/constants/webdav'

/**
 * 数据同步服务
 */
export class SyncService {
  private static instance: SyncService
  private client: ChromeWebDAVClient | null = null

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): SyncService {
    if (!SyncService.instance) {
      SyncService.instance = new SyncService()
    }
    return SyncService.instance
  }

  /**
   * 执行数据同步
   */
  async sync(): Promise<SyncResult> {
    const startTime = Date.now()
    
    try {
      // 1. 检查WebDAV配置
      const config = await getWebDAVConfig()
      if (!config || !config.enabled) {
        return this.createSyncResult(false, 'NO_ACTION', 'WebDAV not configured or disabled', startTime)
      }

      // 2. 初始化WebDAV客户端
      this.client = createChromeWebDAVClient(config)

      // 3. 测试连接
      const connectionTest = await this.client.testConnection()
      if (!connectionTest.success) {
        return this.createSyncResult(false, 'NO_ACTION', connectionTest.error || 'Connection failed', startTime)
      }

      // 4. 确保备份目录存在
      await this.ensureBackupDirectory()

      // 5. 获取本地数据
      const localData = await this.getLocalData()
      
      // 6. 获取远程数据
      const remoteData = await this.getRemoteData()

      // 7. 执行冲突检测
      const conflictType = this.detectConflict(localData, remoteData)

      // 8. 根据冲突类型执行同步操作
      const syncResult = await this.executeSync(conflictType, localData, remoteData, startTime)

      this.logDebug('Sync completed', syncResult)
      return syncResult

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown sync error'
      this.logDebug('Sync failed:', error)
      return this.createSyncResult(false, 'NO_ACTION', errorMessage, startTime)
    }
  }

  /**
   * 确保备份目录存在 - 改进版本
   */
  private async ensureBackupDirectory(): Promise<void> {
    if (!this.client) throw new Error('WebDAV client not initialized')

    const dirExists = await this.client.exists('')
    this.logDebug('Backup directory exists check:', dirExists)
    
    if (!dirExists.data) {
      this.logDebug('Creating backup directory...')
      const createResult = await this.client.createDirectory('')
      
      if (!createResult.success) {
        const errorMsg = `Failed to create backup directory: ${createResult.error}`
        this.logDebug(errorMsg)
        throw new Error(errorMsg)
      }
      
      this.logDebug('Backup directory created successfully')
    } else {
      this.logDebug('Backup directory already exists')
    }
  }

  /**
   * 获取本地数据
   */
  private async getLocalData(): Promise<SyncDataWrapper<BackupData> | null> {
    try {
      // 获取主要数据
      const result = await chrome.storage.local.get([
        'bookmarks',
        'categories', 
        'settings',
        'networkMode'
      ])

      if (!result.bookmarks && !result.categories) {
        this.logDebug('No local data found')
        return null
      }

      const content: BackupData = {
        bookmarks: result.bookmarks || [],
        categories: result.categories || [],
        settings: result.settings || {},
        networkMode: result.networkMode || 'internal',
        timestamp: Date.now(),
        version: '1.0.0'
      }

      // 获取或创建元数据
      const metadataResult = await chrome.storage.local.get(STORAGE_KEYS.DATA_METADATA)
      let metadata: DataMetadata = metadataResult[STORAGE_KEYS.DATA_METADATA]

      if (!metadata) {
        metadata = this.createMetadata(content)
        await chrome.storage.local.set({ [STORAGE_KEYS.DATA_METADATA]: metadata })
      }

      return { content, metadata }

    } catch (error) {
      this.logDebug('Failed to get local data:', error)
      return null
    }
  }

  /**
   * 获取远程数据
   */
  private async getRemoteData(): Promise<SyncDataWrapper<BackupData> | null> {
    if (!this.client) throw new Error('WebDAV client not initialized')

    try {
      // 检查数据文件是否存在
      const dataExists = await this.client.exists(WEBDAV_PATHS.DATA_FILE)
      if (!dataExists.data) {
        this.logDebug('No remote data file found')
        return null
      }

      // 下载数据文件
      const dataResult = await this.client.downloadFile(WEBDAV_PATHS.DATA_FILE)
      if (!dataResult.success || !dataResult.data) {
        throw new Error(`Failed to download remote data: ${dataResult.error}`)
      }

      // 下载元数据文件
      const metadataExists = await this.client.exists(WEBDAV_PATHS.METADATA_FILE)
      let metadata: DataMetadata

      if (metadataExists.data) {
        const metadataResult = await this.client.downloadFile(WEBDAV_PATHS.METADATA_FILE)
        if (metadataResult.success && metadataResult.data) {
          metadata = JSON.parse(metadataResult.data)
        } else {
          // 元数据文件损坏，从数据文件重新生成
          const content = JSON.parse(dataResult.data)
          metadata = this.createMetadata(content)
        }
      } else {
        // 没有元数据文件，从数据文件生成
        const content = JSON.parse(dataResult.data)
        metadata = this.createMetadata(content)
      }

      const content: BackupData = JSON.parse(dataResult.data)

      // 验证数据完整性
      if (!this.validateDataIntegrity(content, metadata)) {
        throw new Error('Remote data integrity check failed')
      }

      return { content, metadata }

    } catch (error) {
      this.logDebug('Failed to get remote data:', error)
      throw error
    }
  }

  /**
   * 检测冲突类型
   */
  private detectConflict(
    localData: SyncDataWrapper<BackupData> | null,
    remoteData: SyncDataWrapper<BackupData> | null
  ): ConflictType {
    // 特殊情况：远程为空
    if (!remoteData) {
      if (localData) {
        return 'UPLOAD_LOCAL'  // 上传本地数据到远程
      } else {
        return 'NO_ACTION'     // 本地远程都空，无需操作
      }
    }

    // 特殊情况：本地为空（理论上不太可能）
    if (!localData && remoteData) {
      return 'DOWNLOAD_REMOTE'   // 下载远程数据到本地
    }

    // 正常情况：本地和远程都有数据
    if (localData && remoteData) {
      if (localData.metadata.hash === remoteData.metadata.hash) {
        return 'NO_CONFLICT'       // 数据相同，跳过同步
      }

      if (localData.metadata.timestamp > remoteData.metadata.timestamp) {
        return 'LOCAL_NEWER'       // 本地更新，上传
      }

      if (remoteData.metadata.timestamp > localData.metadata.timestamp) {
        return 'REMOTE_NEWER'      // 远程更新，下载
      }
    }

    // 默认情况
    return 'NO_ACTION'
  }

  /**
   * 执行同步操作
   */
  private async executeSync(
    conflictType: ConflictType,
    localData: SyncDataWrapper<BackupData> | null,
    remoteData: SyncDataWrapper<BackupData> | null,
    startTime: number
  ): Promise<SyncResult> {
    switch (conflictType) {
      case 'NO_ACTION':
      case 'NO_CONFLICT':
        return this.createSyncResult(true, conflictType, undefined, startTime, 'skip')

      case 'UPLOAD_LOCAL':
      case 'LOCAL_NEWER':
        if (localData) {
          await this.uploadData(localData)
          return this.createSyncResult(true, conflictType, undefined, startTime, 'upload', {
            localTimestamp: localData.metadata.timestamp,
            remoteTimestamp: remoteData?.metadata.timestamp,
            hasChanges: true
          })
        }
        break

      case 'DOWNLOAD_REMOTE':
      case 'REMOTE_NEWER':
        if (remoteData) {
          await this.downloadData(remoteData)
          return this.createSyncResult(true, conflictType, undefined, startTime, 'download', {
            localTimestamp: localData?.metadata.timestamp,
            remoteTimestamp: remoteData.metadata.timestamp,
            hasChanges: true
          })
        }
        break
    }

    return this.createSyncResult(false, conflictType, 'Failed to execute sync operation', startTime)
  }

  /**
   * 上传数据到远程
   */
  private async uploadData(data: SyncDataWrapper<BackupData>): Promise<void> {
    if (!this.client) throw new Error('WebDAV client not initialized')

    // 上传数据文件
    const dataContent = JSON.stringify(data.content, null, 2)
    const dataResult = await this.client.uploadFile(WEBDAV_PATHS.DATA_FILE, dataContent)
    if (!dataResult.success) {
      throw new Error(`Failed to upload data: ${dataResult.error}`)
    }

    // 上传元数据文件
    const metadataContent = JSON.stringify(data.metadata, null, 2)
    const metadataResult = await this.client.uploadFile(WEBDAV_PATHS.METADATA_FILE, metadataContent)
    if (!metadataResult.success) {
      throw new Error(`Failed to upload metadata: ${metadataResult.error}`)
    }

    this.logDebug('Data uploaded successfully')
  }

  /**
   * 从远程下载数据
   */
  private async downloadData(data: SyncDataWrapper<BackupData>): Promise<void> {
    try {
      // 备份当前数据
      await this.backupLocalData()

      // 保存远程数据到本地
      await chrome.storage.local.set({
        bookmarks: data.content.bookmarks,
        categories: data.content.categories,
        settings: data.content.settings,
        networkMode: data.content.networkMode,
        [STORAGE_KEYS.DATA_METADATA]: data.metadata
      })

      this.logDebug('Data downloaded successfully')

    } catch (error) {
      this.logDebug('Failed to download data, restoring backup:', error)
      // 这里可以实现从备份恢复的逻辑
      throw error
    }
  }

  /**
   * 备份本地数据
   */
  private async backupLocalData(): Promise<void> {
    try {
      const timestamp = Date.now()
      const result = await chrome.storage.local.get([
        'bookmarks',
        'categories',
        'settings', 
        'networkMode'
      ])

      const backupKey = `backup_${timestamp}`
      await chrome.storage.local.set({ [backupKey]: result })
      
      this.logDebug(`Local data backed up with key: ${backupKey}`)
    } catch (error) {
      this.logDebug('Failed to backup local data:', error)
    }
  }

  /**
   * 创建数据元信息
   */
  private createMetadata(content: BackupData): DataMetadata {
    const contentStr = JSON.stringify(content)
    const hash = this.generateHash(contentStr)
    const timestamp = Date.now()

    return {
      timestamp,
      hash,
      version: 1,
      size: contentStr.length,
      createdAt: timestamp,
      updatedAt: timestamp
    }
  }

  /**
   * 生成数据哈希
   */
  private generateHash(content: string): string {
    // 简单的哈希算法（生产环境建议使用更强的算法）
    let hash = 0
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash).toString(36)
  }

  /**
   * 验证数据完整性
   */
  private validateDataIntegrity(content: BackupData, metadata: DataMetadata): boolean {
    const contentStr = JSON.stringify(content)
    const calculatedHash = this.generateHash(contentStr)
    return calculatedHash === metadata.hash
  }

  /**
   * 创建同步结果
   */
  private createSyncResult(
    success: boolean,
    conflictType: ConflictType,
    error?: string,
    startTime: number = Date.now(),
    action?: 'upload' | 'download' | 'skip',
    changes?: {
      localTimestamp?: number
      remoteTimestamp?: number
      hasChanges: boolean
    }
  ): SyncResult {
    return {
      success,
      conflictType,
      action,
      error,
      startTime,
      endTime: Date.now(),
      changes
    }
  }

  /**
   * 调试日志
   */
  private logDebug(message: string, data?: any): void {
    if (DEBUG_OPTIONS.ENABLE_SYNC_LOGS) {
      console.log(`${DEBUG_OPTIONS.LOG_PREFIX}[Sync] ${message}`, data)
    }
  }
}

/**
 * 获取同步服务实例
 */
export function getSyncService(): SyncService {
  return SyncService.getInstance()
}

/**
 * 便捷方法：执行同步
 */
export async function executeSync(): Promise<SyncResult> {
  return getSyncService().sync()
}
