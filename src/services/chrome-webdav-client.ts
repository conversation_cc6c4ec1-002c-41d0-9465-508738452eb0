/**
 * Chrome扩展专用WebDAV客户端
 * 使用chrome.runtime.sendMessage与background script通信
 */

import type { 
  WebDAVConfig, 
  WebDAVResponse, 
  WebDAVFileInfo, 
  WebDAVClientOptions,
  WebDAVOperation 
} from '@/types/webdav'

import { 
  WEBDAV_DEFAULTS,
  WEBDAV_STATUS_CODES,
  WEBDAV_ERROR_CODES,
  WEBDAV_HEADERS,
  WEBDAV_CONTENT_TYPES,
  DEBUG_OPTIONS
} from '@/constants/webdav'

/**
 * Chrome扩展WebDAV客户端
 * 通过background script发送请求，避免浏览器限制
 */
export class ChromeWebDAVClient {
  private config: WebDAVConfig
  private options: WebDAVClientOptions

  constructor(config: WebDAVConfig, options: WebDAVClientOptions = {}) {
    this.config = config
    this.options = {
      timeout: WEBDAV_DEFAULTS.TIMEOUT,
      maxRetries: WEBDAV_DEFAULTS.MAX_RETRIES,
      retryDelay: WEBDAV_DEFAULTS.RETRY_DELAY,
      strictSSL: true,
      ...options
    }
  }

  /**
   * 测试WebDAV连接
   */
  async testConnection(): Promise<WebDAVResponse<boolean>> {
    return this.sendToBackground('WEBDAV_TEST_CONNECTION', {
      config: this.config
    })
  }

  /**
   * 创建目录
   */
  async createDirectory(path: string): Promise<WebDAVResponse<boolean>> {
    return this.sendToBackground('WEBDAV_CREATE_DIRECTORY', {
      config: this.config,
      path
    })
  }

  /**
   * 检查文件或目录是否存在
   */
  async exists(path: string): Promise<WebDAVResponse<boolean>> {
    return this.sendToBackground('WEBDAV_EXISTS', {
      config: this.config,
      path
    })
  }

  /**
   * 获取文件信息
   */
  async getFileInfo(path: string): Promise<WebDAVResponse<WebDAVFileInfo>> {
    return this.sendToBackground('WEBDAV_GET_FILE_INFO', {
      config: this.config,
      path
    })
  }

  /**
   * 上传文件
   */
  async uploadFile(path: string, content: string): Promise<WebDAVResponse<boolean>> {
    return this.sendToBackground('WEBDAV_UPLOAD_FILE', {
      config: this.config,
      path,
      content
    })
  }

  /**
   * 下载文件
   */
  async downloadFile(path: string): Promise<WebDAVResponse<string>> {
    return this.sendToBackground('WEBDAV_DOWNLOAD_FILE', {
      config: this.config,
      path
    })
  }

  /**
   * 发送消息到background script
   */
  private async sendToBackground(action: string, data: any): Promise<any> {
    try {
      this.logDebug(`Sending to background: ${action}`, data)

      const response = await chrome.runtime.sendMessage({
        action,
        data,
        timestamp: Date.now()
      })

      this.logDebug(`Background response:`, response)

      if (response && response.success !== undefined) {
        return response
      } else {
        throw new Error('Invalid response from background script')
      }
    } catch (error) {
      this.logDebug(`Background communication error:`, error)
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Background communication failed'
      }
    }
  }

  /**
   * 构建完整URL
   */
  buildUrl(path: string): string {
    const baseUrl = this.config.serverUrl.replace(/\/$/, '')
    const backupPath = this.config.backupPath.replace(/^\//, '').replace(/\/$/, '')
    const filePath = path.replace(/^\//, '')
    
    return `${baseUrl}/${backupPath}${filePath ? '/' + filePath : ''}`
  }

  /**
   * 调试日志
   */
  private logDebug(message: string, data?: any): void {
    if (DEBUG_OPTIONS.ENABLE_WEBDAV_LOGS) {
      console.log(`${DEBUG_OPTIONS.LOG_PREFIX}[ChromeClient] ${message}`, data)
    }
  }
}

/**
 * 创建Chrome扩展WebDAV客户端实例
 */
export function createChromeWebDAVClient(
  config: WebDAVConfig, 
  options?: WebDAVClientOptions
): ChromeWebDAVClient {
  return new ChromeWebDAVClient(config, options)
}
