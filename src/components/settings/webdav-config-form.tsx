/**
 * WebDAV配置表单组件
 */

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  Cloud, 
  Check, 
  CloudOff, 
  Loader2, 
  Eye, 
  EyeOff,
  AlertCircle,
  CheckCircle,
  Info
} from 'lucide-react'

import { useWebDAVConfig } from '@/hooks/use-webdav-config'
import type { WebDAVConfig } from '@/types/webdav'
import { WEBDAV_DEFAULTS } from '@/constants/webdav'

/**
 * 连接状态显示组件
 */
const ConnectionStatusBadge: React.FC<{ 
  status: string
  error?: string | null 
}> = ({ status, error }) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'connected':
        return {
          variant: 'default' as const,
          icon: Check,
          text: '已连接',
          className: 'bg-green-100 text-green-800 hover:bg-green-100'
        }
      case 'connecting':
        return {
          variant: 'secondary' as const,
          icon: Loader2,
          text: '连接中...',
          className: 'bg-blue-100 text-blue-800'
        }
      case 'error':
        return {
          variant: 'destructive' as const,
          icon: CloudOff,
          text: '连接失败',
          className: ''
        }
      case 'unauthorized':
        return {
          variant: 'destructive' as const,
          icon: AlertCircle,
          text: '认证失败',
          className: ''
        }
      case 'timeout':
        return {
          variant: 'secondary' as const,
          icon: CloudOff,
          text: '连接超时',
          className: ''
        }
      default:
        return {
          variant: 'outline' as const,
          icon: Cloud,
          text: '未连接',
          className: ''
        }
    }
  }

  const config = getStatusConfig()
  const Icon = config.icon

  return (
    <div className="flex flex-col gap-1">
      <Badge variant={config.variant} className={config.className}>
        <Icon className={`w-3 h-3 mr-1 ${status === 'connecting' ? 'animate-spin' : ''}`} />
        {config.text}
      </Badge>
      {error && status !== 'connecting' && (
        <p className="text-xs text-red-600 max-w-xs truncate" title={error}>
          {error}
        </p>
      )}
    </div>
  )
}

/**
 * WebDAV配置表单组件
 */
export const WebDAVConfigForm: React.FC = () => {
  const {
    config,
    loading,
    connectionStatus,
    error,
    hasValidConfig,
    saveConfig,
    testConnection,
    deleteConfig,
    clearError
  } = useWebDAVConfig()

  // 表单状态
  const [formData, setFormData] = useState<Partial<WebDAVConfig>>({
    serverUrl: '',
    username: '',
    password: '',
    backupPath: WEBDAV_DEFAULTS.BACKUP_PATH,
    enabled: true,
    timeout: WEBDAV_DEFAULTS.TIMEOUT
  })

  // UI状态
  const [showPassword, setShowPassword] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [isTesting, setIsTesting] = useState(false)
  const [saveSuccess, setSaveSuccess] = useState(false)

  /**
   * 初始化表单数据
   */
  useEffect(() => {
    if (config) {
      setFormData({
        serverUrl: config.serverUrl || '',
        username: config.username || '',
        password: config.password || '',
        backupPath: config.backupPath || WEBDAV_DEFAULTS.BACKUP_PATH,
        enabled: config.enabled !== undefined ? config.enabled : true,
        timeout: config.timeout || WEBDAV_DEFAULTS.TIMEOUT
      })
    }
  }, [config])

  /**
   * 清除成功状态
   */
  useEffect(() => {
    if (saveSuccess) {
      const timer = setTimeout(() => setSaveSuccess(false), 3000)
      return () => clearTimeout(timer)
    }
  }, [saveSuccess])

  /**
   * 处理输入变化
   */
  const handleInputChange = (field: keyof WebDAVConfig) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = field === 'enabled' ? event.target.checked : 
                  field === 'timeout' ? Number(event.target.value) : 
                  event.target.value

    setFormData(prev => ({
      ...prev,
      [field]: value
    }))

    // 清除错误状态
    if (error) {
      clearError()
    }
  }

  /**
   * 验证表单
   */
  const validateForm = (): boolean => {
    if (!formData.serverUrl?.trim()) {
      return false
    }
    if (!formData.username?.trim()) {
      return false
    }
    if (!formData.password?.trim()) {
      return false
    }
    return true
  }

  /**
   * 保存配置
   */
  const handleSave = async () => {
    if (!validateForm()) {
      return
    }

    setIsSaving(true)
    try {
      const success = await saveConfig(formData)
      if (success) {
        setSaveSuccess(true)
      }
    } finally {
      setIsSaving(false)
    }
  }

  /**
   * 测试连接
   */
  const handleTestConnection = async () => {
    if (!validateForm()) {
      return
    }

    setIsTesting(true)
    try {
      await testConnection(formData)
    } catch (error) {
      // 如果是网络错误，可能是浏览器弹出了认证框
      if (error instanceof Error && error.message.includes('网络')) {
        // 可以在这里添加额外的错误处理
      }
    } finally {
      setIsTesting(false)
    }
  }

  /**
   * 删除配置
   */
  const handleDelete = async () => {
    if (confirm('确定要删除WebDAV配置吗？这将清除所有相关设置。')) {
      await deleteConfig()
      // 重置表单
      setFormData({
        serverUrl: '',
        username: '',
        password: '',
        backupPath: WEBDAV_DEFAULTS.BACKUP_PATH,
        enabled: true,
        timeout: WEBDAV_DEFAULTS.TIMEOUT
      })
    }
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin mr-2" />
            <span>加载配置中...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Cloud className="w-5 h-5" />
              WebDAV备份配置
            </CardTitle>
            <CardDescription>
              配置WebDAV服务器信息，实现数据云端备份同步
            </CardDescription>
          </div>
          <ConnectionStatusBadge 
            status={connectionStatus} 
            error={error}
          />
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* 成功提示 */}
        {saveSuccess && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              配置保存成功！
            </AlertDescription>
          </Alert>
        )}

        {/* 错误提示 */}
        {error && !saveSuccess && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* 启用开关 */}
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="enabled">启用WebDAV同步</Label>
            <p className="text-sm text-muted-foreground">
              开启后将自动同步数据到WebDAV服务器
            </p>
          </div>
          <Switch
            id="enabled"
            checked={formData.enabled}
            onCheckedChange={(checked) => {
              setFormData(prev => ({ ...prev, enabled: checked }))
            }}
          />
        </div>

        <Separator />

        {/* 服务器配置 */}
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="serverUrl">服务器地址</Label>
            <Input
              id="serverUrl"
              type="url"
              placeholder="https://your-webdav-server.com"
              value={formData.serverUrl}
              onChange={handleInputChange('serverUrl')}
              disabled={!formData.enabled}
            />
            <p className="text-sm text-muted-foreground">
              WebDAV服务器的完整URL地址，例如：
              <br />• NextCloud: <code>https://yourcloud.com/remote.php/dav/files/username/</code>
              <br />• 坚果云: <code>https://dav.jianguoyun.com/dav/</code>
            </p>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="username">用户名</Label>
              <Input
                id="username"
                type="text"
                placeholder="用户名"
                value={formData.username}
                onChange={handleInputChange('username')}
                disabled={!formData.enabled}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">密码</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="密码"
                  value={formData.password}
                  onChange={handleInputChange('password')}
                  disabled={!formData.enabled}
                  className="pr-10"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={!formData.enabled}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="backupPath">备份路径</Label>
            <Input
              id="backupPath"
              type="text"
              placeholder="/dualtab-backup"
              value={formData.backupPath}
              onChange={handleInputChange('backupPath')}
              disabled={!formData.enabled}
            />
            <p className="text-sm text-muted-foreground">
              数据备份在服务器上的存储路径
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="timeout">连接超时（秒）</Label>
            <Input
              id="timeout"
              type="number"
              min="5"
              max="120"
              value={Math.floor((formData.timeout || WEBDAV_DEFAULTS.TIMEOUT) / 1000)}
              onChange={(e) => {
                const seconds = Number(e.target.value)
                setFormData(prev => ({ ...prev, timeout: seconds * 1000 }))
              }}
              disabled={!formData.enabled}
            />
          </div>
        </div>

        <Separator />

        {/* 操作按钮 */}
        <div className="flex flex-wrap gap-3">
          <Button
            onClick={handleTestConnection}
            disabled={!formData.enabled || !validateForm() || isTesting}
            variant="outline"
          >
            {isTesting ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Cloud className="w-4 h-4 mr-2" />
            )}
            测试连接
          </Button>

          <Button
            onClick={handleSave}
            disabled={!validateForm() || isSaving}
          >
            {isSaving ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <CheckCircle className="w-4 h-4 mr-2" />
            )}
            保存配置
          </Button>

          {hasValidConfig && (
            <Button
              onClick={handleDelete}
              variant="destructive"
              disabled={isSaving}
            >
              删除配置
            </Button>
          )}
        </div>

        {/* 说明信息 */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            <strong>使用说明：</strong>
            <ul className="mt-2 space-y-1 text-sm">
              <li>• 支持标准WebDAV协议的服务器（如NextCloud、ownCloud、坚果云等）</li>
              <li>• 首次配置完成后会自动同步现有数据</li>
              <li>• 数据采用HTTPS加密传输，密码本地加密存储</li>
              <li>• <strong>通过扩展的后台服务处理请求，避免浏览器限制</strong></li>
              <li>• 支持自动冲突检测和解决</li>
              <li>• 建议定期备份，避免数据丢失</li>
            </ul>
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  )
}

export default WebDAVConfigForm
