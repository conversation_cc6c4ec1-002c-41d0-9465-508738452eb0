/**
 * 同步状态显示组件
 */

import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import {
  RefreshCw,
  Clock,
  CheckCircle,
  AlertCircle,
  Upload,
  Download,
  Minus,
  Loader2
} from 'lucide-react'

import { useSyncStatus } from '@/hooks/use-sync-status'
import { useWebDAVConfigState } from '@/hooks/use-webdav-config'
import type { SyncStatus, ConflictType } from '@/types/sync'

/**
 * 同步状态标识组件
 */
const SyncStatusBadge: React.FC<{ 
  status: SyncStatus 
  isInProgress: boolean 
}> = ({ status, isInProgress }) => {
  const getStatusConfig = () => {
    if (isInProgress) {
      return {
        variant: 'secondary' as const,
        icon: Loader2,
        text: '同步中...',
        className: 'bg-blue-100 text-blue-800'
      }
    }

    switch (status) {
      case 'success':
        return {
          variant: 'default' as const,
          icon: CheckCircle,
          text: '同步成功',
          className: 'bg-green-100 text-green-800 hover:bg-green-100'
        }
      case 'error':
        return {
          variant: 'destructive' as const,
          icon: AlertCircle,
          text: '同步失败',
          className: ''
        }
      case 'conflict':
        return {
          variant: 'secondary' as const,
          icon: AlertCircle,
          text: '存在冲突',
          className: 'bg-yellow-100 text-yellow-800'
        }
      default:
        return {
          variant: 'outline' as const,
          icon: Minus,
          text: '未同步',
          className: ''
        }
    }
  }

  const config = getStatusConfig()
  const Icon = config.icon

  return (
    <Badge variant={config.variant} className={config.className}>
      <Icon className={`w-3 h-3 mr-1 ${isInProgress ? 'animate-spin' : ''}`} />
      {config.text}
    </Badge>
  )
}

/**
 * 冲突类型显示组件
 */
const ConflictTypeBadge: React.FC<{ conflictType: ConflictType }> = ({ conflictType }) => {
  const getConflictConfig = () => {
    switch (conflictType) {
      case 'UPLOAD_LOCAL':
      case 'LOCAL_NEWER':
        return {
          icon: Upload,
          text: '上传本地',
          className: 'bg-blue-100 text-blue-800'
        }
      case 'DOWNLOAD_REMOTE':
      case 'REMOTE_NEWER':
        return {
          icon: Download,
          text: '下载远程',
          className: 'bg-purple-100 text-purple-800'
        }
      case 'NO_CONFLICT':
        return {
          icon: CheckCircle,
          text: '无冲突',
          className: 'bg-green-100 text-green-800'
        }
      case 'NO_ACTION':
        return {
          icon: Minus,
          text: '无操作',
          className: 'bg-gray-100 text-gray-800'
        }
      default:
        return {
          icon: AlertCircle,
          text: '未知',
          className: 'bg-gray-100 text-gray-800'
        }
    }
  }

  const config = getConflictConfig()
  const Icon = config.icon

  return (
    <Badge variant="outline" className={config.className}>
      <Icon className="w-3 h-3 mr-1" />
      {config.text}
    </Badge>
  )
}

/**
 * 格式化时间显示
 */
const formatTime = (timestamp: number | null): string => {
  if (!timestamp) return '从未'
  
  const now = Date.now()
  const diff = now - timestamp
  
  if (diff < 60000) {
    return '刚刚'
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return new Date(timestamp).toLocaleString('zh-CN')
  }
}

/**
 * 同步状态显示组件
 */
export const SyncStatusDisplay: React.FC = () => {
  const {
    status,
    isInProgress,
    lastSyncTime,
    lastSyncResult,
    error,
    progress,
    executeSync,
    clearError
  } = useSyncStatus()

  const { hasValidConfig, config } = useWebDAVConfigState()

  /**
   * 手动同步
   */
  const handleManualSync = async () => {
    if (error) {
      clearError()
    }
    await executeSync()
  }

  // 如果没有有效配置，显示提示
  if (!hasValidConfig) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <AlertCircle className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">未配置WebDAV</h3>
            <p className="text-muted-foreground">
              请先配置WebDAV服务器信息才能使用同步功能
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // 如果配置存在但未启用
  if (config && !config.enabled) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <RefreshCw className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">同步已禁用</h3>
            <p className="text-muted-foreground">
              WebDAV同步功能当前已禁用，请在配置中启用
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="w-5 h-5" />
            同步状态
          </CardTitle>
          <SyncStatusBadge status={status} isInProgress={isInProgress} />
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 同步进度 */}
        {isInProgress && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>同步进度</span>
              <span>{progress}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        )}

        {/* 错误信息 */}
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-start gap-2">
              <AlertCircle className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-red-700">
                <strong>同步失败：</strong>
                <p className="mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* 最后同步信息 */}
        <div className="space-y-3">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">最后同步时间</span>
            <span className="flex items-center gap-1">
              <Clock className="w-3 h-3" />
              {formatTime(lastSyncTime)}
            </span>
          </div>

          {lastSyncResult && (
            <>
              <Separator />
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">同步类型</span>
                  <ConflictTypeBadge conflictType={lastSyncResult.conflictType} />
                </div>

                {lastSyncResult.action && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">执行操作</span>
                    <span className="font-medium">
                      {lastSyncResult.action === 'upload' ? '上传' :
                       lastSyncResult.action === 'download' ? '下载' : '跳过'}
                    </span>
                  </div>
                )}

                {lastSyncResult.changes && (
                  <div className="text-xs text-muted-foreground">
                    <div className="flex justify-between">
                      <span>本地更新时间：</span>
                      <span>
                        {lastSyncResult.changes.localTimestamp ? 
                          formatTime(lastSyncResult.changes.localTimestamp) : '无'
                        }
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>远程更新时间：</span>
                      <span>
                        {lastSyncResult.changes.remoteTimestamp ? 
                          formatTime(lastSyncResult.changes.remoteTimestamp) : '无'
                        }
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </>
          )}
        </div>

        <Separator />

        {/* 操作按钮 */}
        <div className="flex gap-2">
          <Button
            onClick={handleManualSync}
            disabled={isInProgress}
            className="flex-1"
          >
            {isInProgress ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="w-4 h-4 mr-2" />
            )}
            {isInProgress ? '同步中...' : '立即同步'}
          </Button>

          {error && (
            <Button
              onClick={clearError}
              variant="outline"
              size="sm"
            >
              清除错误
            </Button>
          )}
        </div>

        {/* 说明信息 */}
        <div className="text-xs text-muted-foreground space-y-1">
          <p>• 新标签页打开时会自动执行同步</p>
          <p>• 同步采用时间戳比较，自动解决大部分冲突</p>
          <p>• 本地数据会在同步前自动备份</p>
        </div>
      </CardContent>
    </Card>
  )
}

export default SyncStatusDisplay
