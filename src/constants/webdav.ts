/**
 * WebDAV相关常量配置
 */

/**
 * WebDAV默认配置
 */
export const WEBDAV_DEFAULTS = {
  /** 默认连接超时时间（毫秒） */
  TIMEOUT: 30000,
  /** 默认最大重试次数 */
  MAX_RETRIES: 3,
  /** 默认重试间隔（毫秒） */
  RETRY_DELAY: 1000,
  /** 默认备份路径 */
  BACKUP_PATH: '/dualtab-backup',
  /** 默认同步间隔（毫秒） - 5分钟 */
  SYNC_INTERVAL: 5 * 60 * 1000,
} as const

/**
 * WebDAV文件路径
 */
export const WEBDAV_PATHS = {
  /** 主数据文件名 */
  DATA_FILE: 'data.json',
  /** 元数据文件名 */
  METADATA_FILE: 'metadata.json',
  /** 历史备份目录 */
  HISTORY_DIR: 'history',
  /** 临时文件前缀 */
  TEMP_PREFIX: '.tmp_',
} as const

/**
 * WebDAV HTTP状态码
 */
export const WEBDAV_STATUS_CODES = {
  /** 成功 */
  OK: 200,
  /** 已创建 */
  CREATED: 201,
  /** 无内容 */
  NO_CONTENT: 204,
  /** 多状态（PROPFIND响应） */
  MULTI_STATUS: 207,
  /** 未授权 */
  UNAUTHORIZED: 401,
  /** 禁止访问 */
  FORBIDDEN: 403,
  /** 未找到 */
  NOT_FOUND: 404,
  /** 方法不允许 */
  METHOD_NOT_ALLOWED: 405,
  /** 冲突 */
  CONFLICT: 409,
  /** 不支持的媒体类型 */
  UNSUPPORTED_MEDIA_TYPE: 415,
  /** 锁定 */
  LOCKED: 423,
  /** 内部服务器错误 */
  INTERNAL_SERVER_ERROR: 500,
  /** 未实现 */
  NOT_IMPLEMENTED: 501,
  /** 服务不可用 */
  SERVICE_UNAVAILABLE: 503,
} as const

/**
 * WebDAV错误代码
 */
export const WEBDAV_ERROR_CODES = {
  /** 连接超时 */
  CONNECTION_TIMEOUT: 'CONNECTION_TIMEOUT',
  /** 网络错误 */
  NETWORK_ERROR: 'NETWORK_ERROR',
  /** 认证失败 */
  AUTH_FAILED: 'AUTH_FAILED',
  /** 服务器错误 */
  SERVER_ERROR: 'SERVER_ERROR',
  /** 文件不存在 */
  FILE_NOT_FOUND: 'FILE_NOT_FOUND',
  /** 目录不存在 */
  DIRECTORY_NOT_FOUND: 'DIRECTORY_NOT_FOUND',
  /** 权限不足 */
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  /** 数据格式错误 */
  INVALID_DATA_FORMAT: 'INVALID_DATA_FORMAT',
  /** 哈希校验失败 */
  HASH_MISMATCH: 'HASH_MISMATCH',
  /** 配置无效 */
  INVALID_CONFIG: 'INVALID_CONFIG',
} as const

/**
 * WebDAV请求头
 */
export const WEBDAV_HEADERS = {
  /** 内容类型 */
  CONTENT_TYPE: 'Content-Type',
  /** 认证 */
  AUTHORIZATION: 'Authorization',
  /** 深度（PROPFIND用） */
  DEPTH: 'Depth',
  /** 覆盖 */
  OVERWRITE: 'Overwrite',
  /** 目标 */
  DESTINATION: 'Destination',
  /** If-None-Match */
  IF_NONE_MATCH: 'If-None-Match',
  /** ETag */
  ETAG: 'ETag',
} as const

/**
 * WebDAV内容类型
 */
export const WEBDAV_CONTENT_TYPES = {
  /** JSON */
  JSON: 'application/json',
  /** XML */
  XML: 'application/xml',
  /** 文本 */
  TEXT: 'text/plain',
  /** 目录 */
  DIRECTORY: 'httpd/unix-directory',
} as const

/**
 * WebDAV XML命名空间
 */
export const WEBDAV_NAMESPACES = {
  /** DAV命名空间 */
  DAV: 'DAV:',
} as const

/**
 * 同步配置默认值
 */
export const SYNC_DEFAULTS = {
  /** 是否启用自动同步 */
  AUTO_SYNC: true,
  /** 同步间隔（毫秒） */
  SYNC_INTERVAL: WEBDAV_DEFAULTS.SYNC_INTERVAL,
  /** 是否在启动时同步 */
  SYNC_ON_STARTUP: true,
  /** 最大同步重试次数 */
  MAX_RETRIES: 3,
  /** 同步超时时间（毫秒） */
  TIMEOUT: 30000,
} as const

/**
 * 存储键名
 */
export const STORAGE_KEYS = {
  /** WebDAV配置 */
  WEBDAV_CONFIG: 'webdav_config',
  /** 同步配置 */
  SYNC_CONFIG: 'sync_config',
  /** 最后同步时间 */
  LAST_SYNC_TIME: 'last_sync_time',
  /** 数据元信息 */
  DATA_METADATA: 'data_metadata',
  /** 备份历史 */
  BACKUP_HISTORY: 'backup_history',
} as const

/**
 * 调试选项
 */
export const DEBUG_OPTIONS = {
  /** 是否启用WebDAV调试日志 */
  ENABLE_WEBDAV_LOGS: process.env.NODE_ENV === 'development',
  /** 是否启用同步调试日志 */
  ENABLE_SYNC_LOGS: process.env.NODE_ENV === 'development',
  /** 日志前缀 */
  LOG_PREFIX: '[WebDAV]',
} as const
