/**
 * WebDAV请求工具 - 使用XMLHttpRequest避免浏览器认证弹窗
 */

import { WEBDAV_HEADERS, WEBDAV_ERROR_CODES } from '@/constants/webdav'
import type { WebDAVOperation } from '@/types/webdav'

/**
 * 请求选项
 */
interface RequestOptions {
  headers?: Record<string, string>
  body?: string
  timeout?: number
}

/**
 * 使用XMLHttpRequest发送WebDAV请求
 * 避免fetch API可能导致的浏览器认证弹窗
 */
export function webdavRequest(
  method: WebDAVOperation,
  url: string,
  auth: string,
  options: RequestOptions = {}
): Promise<{
  status: number
  text: () => Promise<string>
  ok: boolean
}> {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()
    
    // 配置请求
    xhr.open(method, url, true)
    
    // 设置认证头
    xhr.setRequestHeader(WEBDAV_HEADERS.AUTHORIZATION, auth)
    
    // 设置其他头部
    if (options.headers) {
      Object.entries(options.headers).forEach(([key, value]) => {
        xhr.setRequestHeader(key, value)
      })
    }
    
    // 设置超时
    if (options.timeout) {
      xhr.timeout = options.timeout
    }
    
    // 监听状态变化
    xhr.onreadystatechange = () => {
      if (xhr.readyState === XMLHttpRequest.DONE) {
        const response = {
          status: xhr.status,
          text: () => Promise.resolve(xhr.responseText),
          ok: xhr.status >= 200 && xhr.status < 300
        }
        resolve(response)
      }
    }
    
    // 监听错误
    xhr.onerror = () => {
      reject(new Error(WEBDAV_ERROR_CODES.NETWORK_ERROR))
    }
    
    // 监听超时
    xhr.ontimeout = () => {
      reject(new Error(WEBDAV_ERROR_CODES.CONNECTION_TIMEOUT))
    }
    
    // 发送请求
    try {
      xhr.send(options.body || null)
    } catch (error) {
      reject(error)
    }
  })
}
