/**
 * 自动同步Hook
 */

import { useEffect, useRef, useCallback } from 'react'
import { useSyncStatus } from './use-sync-status'
import { useWebDAVConfigState } from './use-webdav-config'
import { SYNC_DEFAULTS } from '@/constants/webdav'

/**
 * 自动同步选项
 */
export interface AutoSyncOptions {
  /** 是否在组件挂载时立即同步 */
  syncOnMount?: boolean
  /** 是否启用定期同步 */
  enablePeriodicSync?: boolean
  /** 同步间隔（毫秒），默认5分钟 */
  syncInterval?: number
  /** 是否只在有有效配置时同步 */
  onlyWithValidConfig?: boolean
  /** 同步前的回调 */
  onBeforeSync?: () => void
  /** 同步后的回调 */
  onAfterSync?: (success: boolean, error?: string) => void
}

/**
 * 自动同步Hook
 */
export function useAutoSync(options: AutoSyncOptions = {}) {
  const {
    syncOnMount = true,
    enablePeriodicSync = false,
    syncInterval = SYNC_DEFAULTS.SYNC_INTERVAL,
    onlyWithValidConfig = true,
    onBeforeSync,
    onAfterSync
  } = options

  const { executeSync, isInProgress } = useSyncStatus()
  const { hasValidConfig, config } = useWebDAVConfigState()
  
  const intervalRef = useRef<NodeJS.Timeout>()
  const hasMountedRef = useRef(false)
  const lastSyncRef = useRef<number>(0)

  /**
   * 检查是否应该执行同步
   */
  const shouldSync = useCallback((): boolean => {
    // 如果正在同步中，跳过
    if (isInProgress) {
      return false
    }

    // 如果要求有效配置但配置无效，跳过
    if (onlyWithValidConfig && !hasValidConfig) {
      return false
    }

    // 如果配置存在但未启用，跳过
    if (config && !config.enabled) {
      return false
    }

    return true
  }, [isInProgress, onlyWithValidConfig, hasValidConfig, config])

  /**
   * 执行同步操作
   */
  const performSync = useCallback(async (reason: 'mount' | 'periodic' | 'manual' = 'manual') => {
    if (!shouldSync()) {
      return
    }

    try {
      onBeforeSync?.()
      
      const result = await executeSync()
      lastSyncRef.current = Date.now()
      
      onAfterSync?.(result.success, result.error)
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`[AutoSync] Sync completed (${reason}):`, result)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Auto sync failed'
      onAfterSync?.(false, errorMessage)
      
      if (process.env.NODE_ENV === 'development') {
        console.warn(`[AutoSync] Sync failed (${reason}):`, error)
      }
    }
  }, [shouldSync, executeSync, onBeforeSync, onAfterSync])

  /**
   * 启动定期同步
   */
  const startPeriodicSync = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }

    if (enablePeriodicSync && syncInterval > 0) {
      intervalRef.current = setInterval(() => {
        performSync('periodic')
      }, syncInterval)

      if (process.env.NODE_ENV === 'development') {
        console.log(`[AutoSync] Periodic sync started (interval: ${syncInterval}ms)`)
      }
    }
  }, [enablePeriodicSync, syncInterval, performSync])

  /**
   * 停止定期同步
   */
  const stopPeriodicSync = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = undefined
      
      if (process.env.NODE_ENV === 'development') {
        console.log('[AutoSync] Periodic sync stopped')
      }
    }
  }, [])

  /**
   * 手动触发同步
   */
  const triggerSync = useCallback(() => {
    performSync('manual')
  }, [performSync])

  /**
   * 组件挂载时的同步
   */
  useEffect(() => {
    if (!hasMountedRef.current) {
      hasMountedRef.current = true
      
      if (syncOnMount) {
        // 延迟一点执行，确保其他Hook都已初始化
        const timer = setTimeout(() => {
          performSync('mount')
        }, 1000)

        return () => clearTimeout(timer)
      }
    }
  }, [syncOnMount, performSync])

  /**
   * 定期同步的启动和停止
   */
  useEffect(() => {
    if (hasValidConfig && config?.enabled) {
      startPeriodicSync()
    } else {
      stopPeriodicSync()
    }

    return stopPeriodicSync
  }, [hasValidConfig, config?.enabled, startPeriodicSync, stopPeriodicSync])

  /**
   * 清理定时器
   */
  useEffect(() => {
    return () => {
      stopPeriodicSync()
    }
  }, [stopPeriodicSync])

  return {
    /** 手动触发同步 */
    triggerSync,
    /** 是否正在同步 */
    isInProgress,
    /** 最后同步时间 */
    lastSyncTime: lastSyncRef.current,
    /** 是否启用了定期同步 */
    isPeriodicSyncEnabled: enablePeriodicSync && !!intervalRef.current,
    /** 启动定期同步 */
    startPeriodicSync,
    /** 停止定期同步 */
    stopPeriodicSync
  }
}

/**
 * 新标签页自动同步Hook - 专门用于新标签页首次打开时同步
 */
export function useNewTabAutoSync() {
  const { executeSync } = useSyncStatus()
  const { hasValidConfig, config } = useWebDAVConfigState()
  
  const hasExecutedRef = useRef(false)

  useEffect(() => {
    // 确保只执行一次
    if (hasExecutedRef.current) {
      return
    }

    // 检查配置是否有效且启用
    if (!hasValidConfig || !config?.enabled) {
      return
    }

    // 延迟执行，等待页面完全加载
    const timer = setTimeout(async () => {
      try {
        hasExecutedRef.current = true
        
        if (process.env.NODE_ENV === 'development') {
          console.log('[NewTabAutoSync] Executing sync on new tab open')
        }
        
        const result = await executeSync()
        
        if (process.env.NODE_ENV === 'development') {
          console.log('[NewTabAutoSync] Sync result:', result)
        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.warn('[NewTabAutoSync] Sync failed:', error)
        }
      }
    }, 2000) // 2秒延迟，确保页面稳定

    return () => clearTimeout(timer)
  }, [hasValidConfig, config?.enabled, executeSync])

  return {
    hasExecuted: hasExecutedRef.current
  }
}

/**
 * 页面可见性变化同步Hook - 页面重新获得焦点时同步
 */
export function useVisibilityChangeSync(options: { 
  enabled?: boolean 
  minInterval?: number // 最小同步间隔，避免频繁同步
} = {}) {
  const { enabled = true, minInterval = 60000 } = options // 默认1分钟间隔
  const { executeSync } = useSyncStatus()
  const { hasValidConfig, config } = useWebDAVConfigState()
  
  const lastSyncRef = useRef<number>(0)

  useEffect(() => {
    if (!enabled) {
      return
    }

    const handleVisibilityChange = async () => {
      // 页面变为可见且配置有效
      if (document.visibilityState === 'visible' && 
          hasValidConfig && 
          config?.enabled) {
        
        const now = Date.now()
        
        // 检查最小间隔
        if (now - lastSyncRef.current < minInterval) {
          return
        }

        try {
          lastSyncRef.current = now
          
          if (process.env.NODE_ENV === 'development') {
            console.log('[VisibilityChangeSync] Executing sync on visibility change')
          }
          
          await executeSync()
        } catch (error) {
          if (process.env.NODE_ENV === 'development') {
            console.warn('[VisibilityChangeSync] Sync failed:', error)
          }
        }
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [enabled, hasValidConfig, config?.enabled, executeSync, minInterval])

  return {
    lastSyncTime: lastSyncRef.current
  }
}
