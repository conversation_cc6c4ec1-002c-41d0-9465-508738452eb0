/**
 * WebDAV配置管理Hook
 */

import { useState, useEffect, useCallback } from 'react'
import type { WebDAVConfig, WebDAVConnectionStatus } from '@/types/webdav'
import { 
  getWebDAVConfigManager, 
  hasValidWebDAVConfig 
} from '@/services/webdav-config'
import { createChromeWebDAVClient } from '@/services/chrome-webdav-client'

/**
 * WebDAV配置Hook状态
 */
export interface UseWebDAVConfigState {
  /** 当前配置 */
  config: WebDAVConfig | null
  /** 是否正在加载 */
  loading: boolean
  /** 连接状态 */
  connectionStatus: WebDAVConnectionStatus
  /** 错误信息 */
  error: string | null
  /** 是否有有效配置 */
  hasValidConfig: boolean
}

/**
 * WebDAV配置Hook操作
 */
export interface UseWebDAVConfigActions {
  /** 保存配置 */
  saveConfig: (config: Partial<WebDAVConfig>) => Promise<boolean>
  /** 测试连接 */
  testConnection: (config?: Partial<WebDAVConfig>) => Promise<boolean>
  /** 删除配置 */
  deleteConfig: () => Promise<boolean>
  /** 更新单个字段 */
  updateField: <K extends keyof WebDAVConfig>(field: K, value: WebDAVConfig[K]) => Promise<boolean>
  /** 重新加载配置 */
  reloadConfig: () => Promise<void>
  /** 清除错误 */
  clearError: () => void
}

/**
 * WebDAV配置Hook返回值
 */
export type UseWebDAVConfigReturn = UseWebDAVConfigState & UseWebDAVConfigActions

/**
 * WebDAV配置管理Hook
 */
export function useWebDAVConfig(): UseWebDAVConfigReturn {
  const [state, setState] = useState<UseWebDAVConfigState>({
    config: null,
    loading: true,
    connectionStatus: 'idle',
    error: null,
    hasValidConfig: false
  })

  const configManager = getWebDAVConfigManager()

  /**
   * 加载配置
   */
  const loadConfig = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }))
      
      const config = await configManager.getConfig()
      const hasValid = await hasValidWebDAVConfig()
      
      setState(prev => ({
        ...prev,
        config,
        hasValidConfig: hasValid,
        loading: false
      }))
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load config'
      setState(prev => ({
        ...prev,
        error: errorMessage,
        loading: false
      }))
    }
  }, [configManager])

  /**
   * 保存配置
   */
  const saveConfig = useCallback(async (newConfig: Partial<WebDAVConfig>): Promise<boolean> => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }))
      
      const success = await configManager.saveConfig(newConfig)
      
      if (success) {
        // 重新加载配置以获取最新状态
        await loadConfig()
        return true
      } else {
        setState(prev => ({
          ...prev,
          error: 'Failed to save configuration',
          loading: false
        }))
        return false
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to save config'
      setState(prev => ({
        ...prev,
        error: errorMessage,
        loading: false
      }))
      return false
    }
  }, [configManager, loadConfig])

  /**
   * 测试连接
   */
  const testConnection = useCallback(async (testConfig?: Partial<WebDAVConfig>): Promise<boolean> => {
    try {
      setState(prev => ({ ...prev, connectionStatus: 'connecting', error: null }))
      
      // 使用提供的配置或当前配置
      const configToTest = testConfig || state.config
      if (!configToTest) {
        setState(prev => ({
          ...prev,
          connectionStatus: 'error',
          error: 'No configuration to test'
        }))
        return false
      }

      // 创建临时客户端进行测试
      const client = createChromeWebDAVClient(configToTest as WebDAVConfig)
      const result = await client.testConnection()

      if (result.success) {
        setState(prev => ({ ...prev, connectionStatus: 'connected' }))
        return true
      } else {
        const status: WebDAVConnectionStatus = 
          result.error?.includes('401') || result.error?.includes('Unauthorized') ? 'unauthorized' :
          result.error?.includes('timeout') ? 'timeout' : 'error'
        
        setState(prev => ({
          ...prev,
          connectionStatus: status,
          error: result.error || 'Connection test failed'
        }))
        return false
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Connection test failed'
      setState(prev => ({
        ...prev,
        connectionStatus: 'error',
        error: errorMessage
      }))
      return false
    }
  }, [state.config])

  /**
   * 删除配置
   */
  const deleteConfig = useCallback(async (): Promise<boolean> => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }))
      
      const success = await configManager.deleteConfig()
      
      if (success) {
        setState(prev => ({
          ...prev,
          config: null,
          hasValidConfig: false,
          connectionStatus: 'idle',
          loading: false
        }))
        return true
      } else {
        setState(prev => ({
          ...prev,
          error: 'Failed to delete configuration',
          loading: false
        }))
        return false
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete config'
      setState(prev => ({
        ...prev,
        error: errorMessage,
        loading: false
      }))
      return false
    }
  }, [configManager])

  /**
   * 更新单个字段
   */
  const updateField = useCallback(async <K extends keyof WebDAVConfig>(
    field: K, 
    value: WebDAVConfig[K]
  ): Promise<boolean> => {
    if (!state.config) {
      setState(prev => ({
        ...prev,
        error: 'No configuration to update'
      }))
      return false
    }

    const updatedConfig = {
      ...state.config,
      [field]: value
    }

    return saveConfig(updatedConfig)
  }, [state.config, saveConfig])

  /**
   * 重新加载配置
   */
  const reloadConfig = useCallback(async (): Promise<void> => {
    // 清除缓存并重新加载
    configManager.clearCache()
    await loadConfig()
  }, [configManager, loadConfig])

  /**
   * 清除错误
   */
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }))
  }, [])

  /**
   * 初始化加载配置
   */
  useEffect(() => {
    loadConfig()
  }, [loadConfig])

  /**
   * 监听配置变化（如果需要的话）
   */
  useEffect(() => {
    const handleStorageChange = (changes: { [key: string]: chrome.storage.StorageChange }) => {
      if (changes.webdav_config) {
        // 配置在其他地方被修改，重新加载
        reloadConfig()
      }
    }

    // 添加存储变化监听器
    chrome.storage.onChanged.addListener(handleStorageChange)

    return () => {
      chrome.storage.onChanged.removeListener(handleStorageChange)
    }
  }, [reloadConfig])

  return {
    // 状态
    config: state.config,
    loading: state.loading,
    connectionStatus: state.connectionStatus,
    error: state.error,
    hasValidConfig: state.hasValidConfig,
    
    // 操作
    saveConfig,
    testConnection,
    deleteConfig,
    updateField,
    reloadConfig,
    clearError
  }
}

/**
 * 简化版Hook - 仅获取配置状态
 */
export function useWebDAVConfigState() {
  const { config, loading, hasValidConfig, error } = useWebDAVConfig()
  
  return {
    config,
    loading,
    hasValidConfig,
    error
  }
}

/**
 * 连接状态Hook - 专门用于显示连接状态
 */
export function useWebDAVConnectionStatus() {
  const { connectionStatus, testConnection, error } = useWebDAVConfig()
  
  return {
    connectionStatus,
    testConnection,
    error
  }
}
