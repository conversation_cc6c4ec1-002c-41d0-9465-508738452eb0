/**
 * 同步状态管理Hook
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import type { 
  SyncStatus, 
  SyncResult, 
  SyncStatusInfo 
} from '@/types/sync'
import { getSyncService } from '@/services/sync-service'
import { getWebDAVConfigManager } from '@/services/webdav-config'
import { STORAGE_KEYS } from '@/constants/webdav'

/**
 * 同步状态Hook状态
 */
export interface UseSyncStatusState {
  /** 当前同步状态 */
  status: SyncStatus
  /** 是否正在同步 */
  isInProgress: boolean
  /** 最后同步时间 */
  lastSyncTime: number | null
  /** 最后同步结果 */
  lastSyncResult: SyncResult | null
  /** 下次自动同步时间 */
  nextSyncTime: number | null
  /** 错误信息 */
  error: string | null
  /** 同步进度（可选，用于显示进度条） */
  progress: number
}

/**
 * 同步状态Hook操作
 */
export interface UseSyncStatusActions {
  /** 手动执行同步 */
  executeSync: () => Promise<SyncResult>
  /** 清除错误状态 */
  clearError: () => void
  /** 重置同步状态 */
  resetStatus: () => void
  /** 更新同步配置 */
  updateSyncConfig: (enabled: boolean) => Promise<void>
}

/**
 * 同步状态Hook返回值
 */
export type UseSyncStatusReturn = UseSyncStatusState & UseSyncStatusActions

/**
 * 同步状态管理Hook
 */
export function useSyncStatus(): UseSyncStatusReturn {
  const [state, setState] = useState<UseSyncStatusState>({
    status: 'idle',
    isInProgress: false,
    lastSyncTime: null,
    lastSyncResult: null,
    nextSyncTime: null,
    error: null,
    progress: 0
  })

  const syncService = getSyncService()
  const isExecutingRef = useRef(false)

  /**
   * 加载同步状态信息
   */
  const loadSyncStatus = useCallback(async () => {
    try {
      const result = await chrome.storage.local.get([
        STORAGE_KEYS.LAST_SYNC_TIME,
        'lastSyncResult'
      ])

      setState(prev => ({
        ...prev,
        lastSyncTime: result[STORAGE_KEYS.LAST_SYNC_TIME] || null,
        lastSyncResult: result.lastSyncResult || null
      }))
    } catch (error) {
      console.warn('Failed to load sync status:', error)
    }
  }, [])

  /**
   * 保存同步结果
   */
  const saveSyncResult = useCallback(async (result: SyncResult) => {
    try {
      await chrome.storage.local.set({
        [STORAGE_KEYS.LAST_SYNC_TIME]: result.endTime,
        lastSyncResult: result
      })
    } catch (error) {
      console.warn('Failed to save sync result:', error)
    }
  }, [])

  /**
   * 执行同步
   */
  const executeSync = useCallback(async (): Promise<SyncResult> => {
    // 防止重复执行
    if (isExecutingRef.current) {
      return {
        success: false,
        conflictType: 'NO_ACTION',
        error: 'Sync already in progress',
        startTime: Date.now(),
        endTime: Date.now()
      }
    }

    try {
      isExecutingRef.current = true
      
      setState(prev => ({
        ...prev,
        status: 'syncing',
        isInProgress: true,
        error: null,
        progress: 0
      }))

      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setState(prev => ({
          ...prev,
          progress: Math.min(prev.progress + 10, 90)
        }))
      }, 200)

      // 执行同步
      const result = await syncService.sync()

      // 清除进度定时器
      clearInterval(progressInterval)

      // 更新状态
      setState(prev => ({
        ...prev,
        status: result.success ? 'success' : 'error',
        isInProgress: false,
        lastSyncTime: result.endTime,
        lastSyncResult: result,
        error: result.error || null,
        progress: 100
      }))

      // 保存同步结果
      await saveSyncResult(result)

      // 如果成功，3秒后重置状态
      if (result.success) {
        setTimeout(() => {
          setState(prev => ({
            ...prev,
            status: 'idle',
            progress: 0
          }))
        }, 3000)
      }

      return result

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Sync failed'
      
      setState(prev => ({
        ...prev,
        status: 'error',
        isInProgress: false,
        error: errorMessage,
        progress: 0
      }))

      return {
        success: false,
        conflictType: 'NO_ACTION',
        error: errorMessage,
        startTime: Date.now(),
        endTime: Date.now()
      }
    } finally {
      isExecutingRef.current = false
    }
  }, [syncService, saveSyncResult])

  /**
   * 清除错误状态
   */
  const clearError = useCallback(() => {
    setState(prev => ({
      ...prev,
      error: null,
      status: prev.status === 'error' ? 'idle' : prev.status
    }))
  }, [])

  /**
   * 重置同步状态
   */
  const resetStatus = useCallback(() => {
    setState(prev => ({
      ...prev,
      status: 'idle',
      isInProgress: false,
      error: null,
      progress: 0
    }))
  }, [])

  /**
   * 更新同步配置
   */
  const updateSyncConfig = useCallback(async (enabled: boolean) => {
    try {
      const configManager = getWebDAVConfigManager()
      await configManager.updateConfigField('enabled', enabled)
    } catch (error) {
      console.warn('Failed to update sync config:', error)
    }
  }, [])

  /**
   * 初始化加载状态
   */
  useEffect(() => {
    loadSyncStatus()
  }, [loadSyncStatus])

  /**
   * 监听存储变化
   */
  useEffect(() => {
    const handleStorageChange = (changes: { [key: string]: chrome.storage.StorageChange }) => {
      if (changes[STORAGE_KEYS.LAST_SYNC_TIME] || changes.lastSyncResult) {
        loadSyncStatus()
      }
    }

    chrome.storage.onChanged.addListener(handleStorageChange)

    return () => {
      chrome.storage.onChanged.removeListener(handleStorageChange)
    }
  }, [loadSyncStatus])

  return {
    // 状态
    status: state.status,
    isInProgress: state.isInProgress,
    lastSyncTime: state.lastSyncTime,
    lastSyncResult: state.lastSyncResult,
    nextSyncTime: state.nextSyncTime,
    error: state.error,
    progress: state.progress,
    
    // 操作
    executeSync,
    clearError,
    resetStatus,
    updateSyncConfig
  }
}

/**
 * 简化版Hook - 仅获取同步状态
 */
export function useSyncStatusState() {
  const { status, isInProgress, lastSyncTime, error } = useSyncStatus()
  
  return {
    status,
    isInProgress,
    lastSyncTime,
    error
  }
}

/**
 * 同步操作Hook - 专门用于触发同步
 */
export function useSyncActions() {
  const { executeSync, clearError, resetStatus } = useSyncStatus()
  
  return {
    executeSync,
    clearError,
    resetStatus
  }
}
