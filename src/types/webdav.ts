/**
 * WebDAV相关类型定义
 */

/**
 * WebDAV服务器配置
 */
export interface WebDAVConfig {
  /** 服务器地址 */
  serverUrl: string
  /** 用户名 */
  username: string
  /** 密码（加密存储） */
  password: string
  /** 备份路径（相对于服务器根目录） */
  backupPath: string
  /** 是否启用WebDAV同步 */
  enabled: boolean
  /** 连接超时时间（毫秒） */
  timeout: number
}

/**
 * WebDAV连接状态
 */
export type WebDAVConnectionStatus = 
  | 'idle'           // 空闲状态
  | 'connecting'     // 连接中
  | 'connected'      // 已连接
  | 'error'          // 连接错误
  | 'unauthorized'   // 认证失败
  | 'timeout'        // 连接超时

/**
 * WebDAV操作类型
 */
export type WebDAVOperation = 
  | 'PROPFIND'       // 查询文件/目录信息
  | 'GET'            // 下载文件
  | 'PUT'            // 上传文件
  | 'MKCOL'          // 创建目录
  | 'DELETE'         // 删除文件/目录

/**
 * WebDAV响应结果
 */
export interface WebDAVResponse<T = any> {
  /** 是否成功 */
  success: boolean
  /** 响应数据 */
  data?: T
  /** 错误信息 */
  error?: string
  /** HTTP状态码 */
  statusCode?: number
}

/**
 * WebDAV文件信息
 */
export interface WebDAVFileInfo {
  /** 文件路径 */
  path: string
  /** 文件名 */
  name: string
  /** 是否为目录 */
  isDirectory: boolean
  /** 文件大小（字节） */
  size: number
  /** 最后修改时间 */
  lastModified: Date
  /** 内容类型 */
  contentType?: string
  /** ETag */
  etag?: string
}

/**
 * WebDAV客户端配置选项
 */
export interface WebDAVClientOptions {
  /** 连接超时时间 */
  timeout?: number
  /** 最大重试次数 */
  maxRetries?: number
  /** 重试间隔（毫秒） */
  retryDelay?: number
  /** 是否验证SSL证书 */
  strictSSL?: boolean
}

/**
 * WebDAV认证信息
 */
export interface WebDAVAuth {
  /** 用户名 */
  username: string
  /** 密码 */
  password: string
  /** 认证类型 */
  type?: 'basic' | 'digest'
}
