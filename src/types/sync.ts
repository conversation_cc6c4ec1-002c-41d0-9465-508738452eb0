/**
 * 数据同步相关类型定义
 */

/**
 * 同步状态
 */
export type SyncStatus = 
  | 'idle'           // 空闲
  | 'syncing'        // 同步中
  | 'success'        // 同步成功
  | 'error'          // 同步失败
  | 'conflict'       // 存在冲突

/**
 * 冲突类型
 */
export type ConflictType = 
  | 'NO_CONFLICT'     // 无冲突
  | 'NO_ACTION'       // 无需操作
  | 'UPLOAD_LOCAL'    // 上传本地数据
  | 'DOWNLOAD_REMOTE' // 下载远程数据
  | 'LOCAL_NEWER'     // 本地更新
  | 'REMOTE_NEWER'    // 远程更新

/**
 * 数据元信息
 */
export interface DataMetadata {
  /** 时间戳 */
  timestamp: number
  /** 数据哈希值 */
  hash: string
  /** 版本号 */
  version: number
  /** 数据大小 */
  size: number
  /** 创建时间 */
  createdAt: number
  /** 最后修改时间 */
  updatedAt: number
}

/**
 * 同步数据包装
 */
export interface SyncDataWrapper<T = any> {
  /** 实际数据内容 */
  content: T
  /** 元信息 */
  metadata: DataMetadata
}

/**
 * 同步结果
 */
export interface SyncResult {
  /** 是否成功 */
  success: boolean
  /** 冲突类型 */
  conflictType: ConflictType
  /** 执行的操作 */
  action?: 'upload' | 'download' | 'skip'
  /** 错误信息 */
  error?: string
  /** 同步开始时间 */
  startTime: number
  /** 同步结束时间 */
  endTime: number
  /** 数据变更详情 */
  changes?: {
    /** 本地数据时间戳 */
    localTimestamp?: number
    /** 远程数据时间戳 */
    remoteTimestamp?: number
    /** 是否有数据变更 */
    hasChanges: boolean
  }
}

/**
 * 同步配置
 */
export interface SyncConfig {
  /** 是否启用自动同步 */
  autoSync: boolean
  /** 同步间隔（毫秒） */
  syncInterval: number
  /** 是否在启动时同步 */
  syncOnStartup: boolean
  /** 最大同步重试次数 */
  maxRetries: number
  /** 同步超时时间（毫秒） */
  timeout: number
}

/**
 * 同步状态信息
 */
export interface SyncStatusInfo {
  /** 当前状态 */
  status: SyncStatus
  /** 最后同步时间 */
  lastSyncTime?: number
  /** 最后同步结果 */
  lastSyncResult?: SyncResult
  /** 下次同步时间 */
  nextSyncTime?: number
  /** 是否正在进行中 */
  isInProgress: boolean
  /** 错误信息 */
  error?: string
}

/**
 * 备份历史记录
 */
export interface BackupHistory {
  /** 备份ID */
  id: string
  /** 备份时间 */
  timestamp: number
  /** 备份大小 */
  size: number
  /** 备份描述 */
  description?: string
  /** 备份类型 */
  type: 'manual' | 'auto'
  /** 是否为当前版本 */
  isCurrent: boolean
}
